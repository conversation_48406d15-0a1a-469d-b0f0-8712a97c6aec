// melody/src/components/project/FolderTree.tsx
import {
  DeleteRounded,
  Download,
  DriveFolderUpload,
  Folder as FolderIcon,
  KeyboardArrowDown,
  KeyboardArrowRight,
  UploadFileOutlined,
} from "@mui/icons-material";
import { Box, Button, CircularProgress } from "@mui/material";
import { useEffect, useState } from "react";
import { Fonts } from "../../constants/typology";
import { File } from "../../models/file";
import { Folder } from "../../models/folder";
import {
  useDeleteFolderMutation,
  useGetFolderContentsMutation,
} from "../../routes/folder";
import { BaseText } from "../base/BaseText";
import Modal from "../base/Modal";
import { FileRow } from "./FileRow";

interface FolderTreeProps {
  versionId: number;
  folders: Folder[];
  files: File[];
  onPlayFile: (file: File, url: string, waveform: number[]) => void;
  setTargetFolderId: (id: number | null) => void;
  setIsAddFileModalOpen: (isOpen: boolean) => void;
  setIsUploadFolderModalOpen: (isOpen: boolean) => void;
  referencedItemId: number | null;
  referencedItemType: "file" | "folder" | null;
  setReferencedItemId: (id: number | null) => void;
  setReferencedItemType: (type: "file" | "folder" | null) => void;
  versionCount: number;
}

interface DeleteFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  folder: TreeNode;
  deleteFolder: (id: number) => void;
}

const DeleteFolderModal = ({
  isOpen,
  onClose,
  folder,
  deleteFolder,
}: DeleteFolderModalProps) => {
  return (
    <Modal title="Delete Folder?" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4 mt-3 text-center">
        {`Are you sure you want to delete folder "${folder.name}" and all its
        contents?`}
      </BaseText>
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={{
            textTransform: "none",
            fontSize: "16px",
            borderColor: "#880000",
            color: "#880000",
          }}
          onClick={() => {
            deleteFolder(folder.id);
            onClose();
          }}
        >
          Confirm
        </Button>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={{
            backgroundColor: "#4338ca",
            textTransform: "none",
            fontSize: "16px",
          }}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

// Helper to build a tree structure
interface TreeNode {
  id: number;
  name: string;
  type: "folder" | "file";
  children?: TreeNode[];
  parentId: number | null;
  depth: number; // Using the depth from database
  fileSize?: number;
  fileType?: string;
}

interface TreeNodeProps {
  node: TreeNode;
  versionId: number;
  expandedFolders: Record<number, boolean>;
  toggleFolder: (id: number) => void;
  files: File[];
  folderDownloading: TreeNode | null;
  setFolderDownloading: (node: TreeNode | null) => void;
  fileDownloading: File | null;
  setFileDownloading: (file: File | null) => void;
  onPlayFile: (file: File, url: string, waveform: number[]) => void;
  onFolderIconClick: (folderId: number) => void;
  onAddFileClick: (folderId: number) => void;
  referencedItemId: number | null;
  referencedItemType: "file" | "folder" | null;
}

const TreeNode: React.FC<TreeNodeProps> = ({
  node,
  versionId,
  expandedFolders,
  toggleFolder,
  files,
  folderDownloading,
  setFolderDownloading,
  fileDownloading,
  setFileDownloading,
  onPlayFile,
  onFolderIconClick,
  onAddFileClick,
  referencedItemId,
  referencedItemType,
}) => {
  const { mutate: deleteFolder } = useDeleteFolderMutation(versionId);
  const { mutate: downloadFolder, isPending: pendingDownloadFolder } =
    useGetFolderContentsMutation();
  const indent = node.depth * 30;
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const [hovered, setHovered] = useState(false);

  if (node.type === "folder") {
    const isExpanded = expandedFolders[node.id];
    const isReferenced =
      referencedItemType === "folder" && referencedItemId === node.id;

    return (
      <>
        <Box key={`folder-${node.id}`}>
          <Box
            className={`flex items-center p-2 hover:bg-[#1a202c] border-l-0 hover:border-l-8 border-l-[#4338ca] hover:translate-x-1 rounded-md group transition-all duration-200 ${isReferenced ? "bg-yellow-500 bg-opacity-30 folder-highlight" : ""
              }`}
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
          >
            <Box
              className="flex items-center flex-grow"
              style={{ marginLeft: `${indent}px` }}
            >
              {/* Arrow icon with click handler */}
              <Box
                className="flex items-center cursor-pointer"
                onClick={() => toggleFolder(node.id)}
              >
                {isExpanded ? (
                  <KeyboardArrowDown
                    fontSize="small"
                    className="text-gray-400 mr-1"
                  />
                ) : (
                  <KeyboardArrowRight
                    fontSize="small"
                    className="text-gray-400 mr-1"
                  />
                )}
              </Box>
              {/* Rest of the folder content without onClick */}
              <Box className="flex items-center">
                <FolderIcon
                  className="text-yellow-400 mr-2"
                  sx={{ fontSize: 27 }}
                />

                <BaseText
                  variant={Fonts.BodySemibold}
                  className="text-slate-300"
                >
                  {node.name}
                </BaseText>
                {hovered && (
                  <>
                    {pendingDownloadFolder &&
                      folderDownloading?.id === node.id ? (
                      <CircularProgress className="ml-2" size={16} />
                    ) : (
                      <Download
                        className="text-slate-400 hover:text-blue-400 cursor-pointer ml-1"
                        sx={{
                          fontSize: 20,
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setFolderDownloading(node);
                          downloadFolder({
                            id: node.id,
                            folderName: node.name,
                          });
                        }}
                      />
                    )}

                    <DriveFolderUpload
                      className="text-slate-400 hover:text-blue-400 cursor-pointer ml-1"
                      sx={{
                        fontSize: 20,
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        onFolderIconClick(node.id);
                      }}
                    />
                    <UploadFileOutlined
                      className="text-slate-400 hover:text-blue-400 cursor-pointer ml-1"
                      sx={{
                        fontSize: 18,
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        onAddFileClick(node.id);
                      }}
                    />
                  </>
                )}
              </Box>
            </Box>
            <Box
              className="flex items-center px-4"
              onClick={(e) => e.stopPropagation()}
            >
              <DeleteRounded
                onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteModalOpen(true);
                }}
                className="text-rose-400 hover:text-red-500 invisible group-hover:visible cursor-pointer z-10"
                fontSize="small"
              />
            </Box>
          </Box>

          {isExpanded && node.children && node.children.length > 0 && (
            <Box>
              {node.children.map((child) => (
                <TreeNode
                  key={child.id}
                  node={child}
                  versionId={versionId}
                  expandedFolders={expandedFolders}
                  toggleFolder={toggleFolder}
                  files={files}
                  folderDownloading={folderDownloading}
                  setFolderDownloading={setFolderDownloading}
                  fileDownloading={fileDownloading}
                  setFileDownloading={setFileDownloading}
                  onPlayFile={onPlayFile}
                  onFolderIconClick={onFolderIconClick}
                  onAddFileClick={onAddFileClick}
                  referencedItemId={referencedItemId}
                  referencedItemType={referencedItemType}
                />
              ))}
            </Box>
          )}
        </Box>

        <DeleteFolderModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          folder={node}
          deleteFolder={(id) => {
            deleteFolder(id, {
              onError: (error) => {
                console.error("Error deleting folder:", error);
                alert("Failed to delete folder. Please try again.");
              },
            });
          }}
        />
      </>
    );
  } else {
    const foundFile = files.find((f) => f.id === node.id);
    return foundFile ? (
      <FileRow
        file={foundFile}
        isLoading={false}
        versionId={versionId}
        indent={indent}
        fileDownloading={fileDownloading}
        setFileDownloading={setFileDownloading}
        onPlayFile={onPlayFile}
        isReferenced={
          referencedItemType === "file" && referencedItemId === node.id
        }
      />
    ) : null;
  }
};

export const FolderTree = ({
  versionId,
  folders,
  files,
  onPlayFile,
  setTargetFolderId,
  setIsAddFileModalOpen,
  setIsUploadFolderModalOpen,
  referencedItemId,
  referencedItemType,
  setReferencedItemId,
  setReferencedItemType,
  versionCount,
}: FolderTreeProps) => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<
    Record<number, boolean>
  >({});

  const [folderDownloading, setFolderDownloading] = useState<TreeNode | null>(
    null
  );

  const [fileDownloading, setFileDownloading] = useState<File | null>(null);

  const onFolderIconClick = (folderId: number) => {
    setTargetFolderId(folderId);
    setIsUploadFolderModalOpen(true);
  };

  const onAddFileClick = (folderId: number) => {
    setTargetFolderId(folderId);
    setIsAddFileModalOpen(true);
  };

  useEffect(() => {
    // Build tree structure
    if (!folders && !files) return;

    // First, create nodes for all folders
    const folderNodes: Record<number, TreeNode> = {};
    folders.forEach((folder) => {
      folderNodes[folder.id] = {
        id: folder.id,
        name: folder.name,
        type: "folder",
        children: [],
        parentId: folder.parentId ?? null,
        depth: folder.depth,
      };
    });

    // Then, create file nodes and associate with parent folders
    const fileNodes: TreeNode[] = files.map((file) => ({
      id: file.id,
      name: file.name,
      type: "file",
      parentId: file.folderId ?? null,
      depth: file.folderId ? folderNodes[file.folderId]?.depth + 1 || 0 : 0,
      fileSize: file.fileSize,
      fileType: file.fileType,
    }));

    // Add file nodes to their parent folders
    fileNodes.forEach((fileNode) => {
      if (fileNode.parentId && folderNodes[fileNode.parentId]) {
        folderNodes[fileNode.parentId].children =
          folderNodes[fileNode.parentId].children || [];
        folderNodes[fileNode.parentId].children!.push(fileNode);
      }
    });

    // Build the hierarchy - add folders to their parents
    Object.values(folderNodes).forEach((folder) => {
      if (folder.parentId && folderNodes[folder.parentId]) {
        folderNodes[folder.parentId].children =
          folderNodes[folder.parentId].children || [];
        folderNodes[folder.parentId].children!.push(folder);
      }
    });

    // Get root folders and files
    const rootNodes = Object.values(folderNodes).filter(
      (folder) => !folder.parentId || !folderNodes[folder.parentId]
    );
    const rootFiles = fileNodes.filter((file) => !file.parentId);
    const roots = [...rootNodes, ...rootFiles];
    setTreeData(roots);

    // Initialize folders that don't exist in expandedFolders yet
    setExpandedFolders((prev) => {
      const newExpanded = { ...prev };
      Object.keys(folderNodes).forEach((id) => {
        const folderId = Number(id);
        if (newExpanded[folderId] === undefined) {
          newExpanded[folderId] = false;
        }
      });
      return newExpanded;
    });
  }, [folders, files]);

  const toggleFolder = (folderId: number) => {
    setExpandedFolders((prev) => ({
      ...prev,
      [folderId]: !prev[folderId],
    }));
  };

  // Add effect to expand folders to show referenced item
  useEffect(() => {
    if (!referencedItemId || !referencedItemType) return;

    const newExpandedFolders = { ...expandedFolders };

    if (referencedItemType === "file") {
      // Find the file and its parent folders
      const referencedFile = files.find((file) => file.id === referencedItemId);
      if (referencedFile && referencedFile.folderId) {
        // Expand all parent folders
        const expandParentFolders = (folderId: number | null) => {
          if (!folderId) return;

          newExpandedFolders[folderId] = true;

          // Find parent folder
          const parentFolder = folders.find((folder) => folder.id === folderId);
          if (parentFolder && parentFolder.parentId) {
            expandParentFolders(parentFolder.parentId);
          }
        };

        expandParentFolders(referencedFile.folderId);
      }
    } else if (referencedItemType === "folder") {
      // Expand all parent folders
      const expandParentFolders = (folderId: number | null) => {
        if (!folderId) return;

        newExpandedFolders[folderId] = true;

        // Find parent folder
        const parentFolder = folders.find((folder) => folder.id === folderId);
        if (parentFolder && parentFolder.parentId) {
          expandParentFolders(parentFolder.parentId);
        }
      };

      expandParentFolders(referencedItemId);
    }

    setExpandedFolders(newExpandedFolders);

    // Clear the reference after a timeout
    const timer = setTimeout(() => {
      setReferencedItemId(null);
      setReferencedItemType(null);
    }, 3000);

    return () => clearTimeout(timer);
  }, [referencedItemId, referencedItemType, files, folders]);

  return (
    <Box className="bg-bglight p-4 rounded-lg">
      {treeData.length > 0 ? (
        treeData.map((node) => (
          <TreeNode
            key={node.id}
            node={node}
            versionId={versionId}
            expandedFolders={expandedFolders}
            toggleFolder={toggleFolder}
            files={files}
            folderDownloading={folderDownloading}
            setFolderDownloading={setFolderDownloading}
            fileDownloading={fileDownloading}
            setFileDownloading={setFileDownloading}
            onPlayFile={onPlayFile}
            onFolderIconClick={onFolderIconClick}
            onAddFileClick={onAddFileClick}
            referencedItemId={referencedItemId}
            referencedItemType={referencedItemType}
          />
        ))
      ) : (
        <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
          {versionCount === 1 && files.length === 0 && folders.length === 0
            ? "For your 1st version, upload your song's stems or final file.\nThey will carry through to the next version."
            : "No files or folders in this version."}
        </BaseText>
      )}
    </Box>
  );
};
