// melody/src/app/(pages)/projects/[id]/page.tsx

"use client";
import { BaseText } from "@/components/base/BaseText";
import Modal from "@/components/base/Modal";
import { UploadProgressBar } from "@/components/common/UploadProgressBar";
import { AudioPlayer } from "@/components/player/AudioPlayer";
import { FolderTree } from "@/components/project/FolderTree";
import TaskModal from "@/components/project/TaskModal";
import { UploadFolderModal } from "@/components/project/UploadFolderModal";
import { BGStyles } from "@/constants/colors";
import { Fonts } from "@/constants/typology";
import { SbUser, useAuth } from "@/contexts/AuthContext";
import { useSocket } from "@/contexts/SocketContext";
import { useUploadProgress } from "@/hooks/useUploadProgress";
import {
  Comment,
  CreateCommentBody,
  UpdateCommentBody,
} from "@/models/comment";
import { File } from "@/models/file";
import { Project } from "@/models/project";
import { ProjectInvite } from "@/models/projectInvite";
import { ProjectUser } from "@/models/projectUser";
import { Task, TaskStatus } from "@/models/task";
import { User } from "@/models/user";
import { CreateVersionBody, Version } from "@/models/version";
import { useCreateConversationMutation } from "@/routes/chat";
import {
  useCreateCommentMutation,
  useDeleteCommentMutation,
  useGetCommentsByProjectQuery,
  useUpdateCommentMutation,
} from "@/routes/comments";
import {
  useCreateFileMutation,
  useGetFilesByVersionQuery,
} from "@/routes/file";
import {
  useGetFolderQuery,
  useGetFoldersByVersionQuery,
} from "@/routes/folder";
import {
  useDownloadProjectMutation,
  useGetProjectQuery,
  useRemoveContributorMutation,
} from "@/routes/project";
import {
  useCreateProjectInviteMutation,
  useGetProjectInvitesQuery,
  useRevokeProjectInviteMutation,
} from "@/routes/projectInvite";
import { queryClient } from "@/routes/queryClient";
import {
  useCreateTaskMutation,
  useDeleteTaskMutation,
  useGetTasksByProjectQuery,
  useUpdateTaskMutation,
} from "@/routes/task";
import { useSearchUsersQuery } from "@/routes/user";
import {
  useCreateVersionMutation,
  useDeleteVersionMutation,
  useGetVersionsByProjectQuery,
  useUpdateVersionMutation,
} from "@/routes/version";
import { buttonStyles } from "@/utils/buttonStyles";
import { FINAL_ALLOWED_EXTENSIONS } from "@/utils/fileTypes";
import {
  Add,
  ArrowBackIosNew,
  Chat,
  DeleteRounded,
  Download,
  Edit,
  FolderCopy,
  InsertDriveFile,
  Lightbulb,
  Settings,
  TaskSharp,
  Textsms,
} from "@mui/icons-material";
import {
  AppBar,
  Avatar,
  Box,
  Button,
  CircularProgress,
  Toolbar,
} from "@mui/material";
import { UseMutateFunction } from "@tanstack/react-query";
import dayjs from "dayjs";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import "react-datepicker/dist/react-datepicker.css";
const statusColors: Record<TaskStatus, string> = {
  [TaskStatus.NOT_STARTED]: "gray",
  [TaskStatus.IN_PROGRESS]: "#ca8a04",
  [TaskStatus.ON_HOLD]: "#92400e",
  [TaskStatus.IN_REVIEW]: "#a855f7",
  [TaskStatus.DONE]: "green",
  [TaskStatus.CLOSED]: "#991b1b",
};

const taskStatusDisplayNames: Record<TaskStatus, string> = {
  [TaskStatus.NOT_STARTED]: "Not Started",
  [TaskStatus.IN_PROGRESS]: "In Progress",
  [TaskStatus.ON_HOLD]: "On Hold",
  [TaskStatus.IN_REVIEW]: "In Review",
  [TaskStatus.DONE]: "Done",
  [TaskStatus.CLOSED]: "Closed",
};

export default function Page() {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const { joinProjectRoom, leaveProjectRoom } = useSocket();

  const [isAddFileModalOpen, setIsAddFileModalOpen] = useState(false);
  const [isUploadFolderModalOpen, setIsUploadFolderModalOpen] = useState(false);
  const [isConfirmVersionModalOpen, setIsConfirmVersionModalOpen] =
    useState(false);
  const [nextVersionNumber, setNextVersionNumber] = useState<number | null>(
    null
  );
  const [newVersionMessage, setNewVersionMessage] = useState<string>("");

  const [editedVersionMessage, setEditedVersionMessage] = useState<string>("");
  const [isVersionMessageBeingEdited, setIsVersionMessageBeingEdited] =
    useState(false);

  const [isDeleteVersionModalOpen, setIsDeleteVersionModalOpen] =
    useState(false);
  const [versionToDelete, setVersionToDelete] = useState<number | null>(null);

  const [selectedFileName, setSelectedFileName] = useState<string | null>(null);
  const [newComment, setNewComment] = useState<string>("");
  const [editingCommentId, setEditingCommentId] = useState<number | null>(null);
  const [editedComment, setEditedComment] = useState<string>("");
  const [isDeleteCommentModalOpen, setIsDeleteCommentModalOpen] =
    useState(false);
  const [commentToDelete, setCommentToDelete] = useState<number | null>(null);
  const [editedTaskName, setEditedTaskName] = useState<string>("");
  const [editedDueDate, setEditedDueDate] = useState<Date | null>(null);
  const [editedAssignee, setEditedAssignee] = useState<string>("");
  const [assigneeQuery, setAssigneeQuery] = useState("");
  const [isDeleteTaskModalOpen, setIsDeleteTaskModalOpen] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<number | null>(null);

  const { mutate: createProjectConversation } = useCreateConversationMutation();

  // Nested uploads
  const [targetFolderId, setTargetFolderId] = useState<number | null>(null);

  const { data: searchedUsers, isLoading: searchingUsers } =
    useSearchUsersQuery(assigneeQuery.length >= 2 ? assigneeQuery : "");

  const [isInviteContributorModalOpen, setIsInviteContributorModalOpen] =
    useState(false);
  const [isRemoveContributorModalOpen, setIsRemoveContributorModalOpen] =
    useState(false);
  const [contributorToRemove, setContributorToRemove] =
    useState<ProjectUser | null>(null);

  const [currentPlayingFile, setCurrentPlayingFile] = useState<File | null>(
    null
  );
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  const [showPlayer, setShowPlayer] = useState(false);

  // Function to open the Add File modal
  const handleAddFileClick = () => {
    setIsAddFileModalOpen(true);
  };

  // Function to close the Add File modal
  const handleAddFileClose = () => {
    setIsAddFileModalOpen(false);
  };

  const handleUploadFolderClick = () => {
    if (!isUploadFolderModalOpen) {
      setIsUploadFolderModalOpen(true);
    }
  };

  const handleUploadFolderClose = () => {
    setIsUploadFolderModalOpen(false); // This was incorrect - setting to false, not true
    refetchFiles();
  };

  // Function to open the Confirm Version modal
  const handleCreateVersionClick = () => {
    const nextVersion =
      (project?.versions ?? []).length > 0
        ? Math.max(...(project?.versions?.map((v) => v.number) ?? [])) + 1
        : 1;
    setNextVersionNumber(nextVersion);
    setIsConfirmVersionModalOpen(true);
  };

  // Function to close the Confirm Version modal
  const handleConfirmVersionClose = () => {
    setIsConfirmVersionModalOpen(false);
  };

  // Function to open the Add Task modal
  const handleAddTaskClick = () => {
    createTask(
      {
        projectId: id, // Use the current project ID
        title: "New Task",
        status: TaskStatus.NOT_STARTED,
        dueDate: dayjs().toDate(), // Current date in ISO format
      },
      {
        onSuccess: (task) => {
          refetchTasks(); // Refetch tasks to update the list
          handleTaskClick(task);
        },
        onError: (error) => {
          console.error("Error creating task:", error);
          alert("Failed to create task. Please try again.");
        },
      }
    );
  };

  // Add these state variables near the other state declarations
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [referencedItemId, setReferencedItemId] = useState<number | null>(null);
  const [referencedItemType, setReferencedItemType] = useState<
    "file" | "folder" | null
  >(null);

  // Add this function with the other handler functions
  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
    setEditedTaskName(task.title);
    setEditedDueDate(task.dueDate ? new Date(task.dueDate) : null);
    setEditedAssignee(task.assignee?.name || "");
    setIsTaskModalOpen(true);
  };

  // Get project
  const params = useParams();
  const id = Number(params.id);
  const {
    data: project,
    isLoading,
    isError,
    refetch: refetchProject,
  } = useGetProjectQuery(id);

  const [version, setVersion] = useState<Version | null>(
    project?.versions[0] || null
  );

  const { refetch: refetchVersions } = useGetVersionsByProjectQuery(id);

  const {
    data: files,
    isLoading: filesLoading,
    refetch: refetchFiles,
  } = useGetFilesByVersionQuery(version?.id ?? 0);

  const { data: comments, refetch: refetchComments } =
    useGetCommentsByProjectQuery(id);

  const { data: tasks, refetch: refetchTasks } = useGetTasksByProjectQuery(id);

  const { mutate: createFile, isPending: isUploading } =
    useCreateFileMutation();

  const { mutate: createVersion, isPending: isCreatingVersion } =
    useCreateVersionMutation();

  const { mutate: createComment } = useCreateCommentMutation();
  const { mutate: updateComment } = useUpdateCommentMutation();
  const { mutate: deleteComment } = useDeleteCommentMutation();

  const { mutate: inviteContributor, isPending: pendingInviteContributor } =
    useCreateProjectInviteMutation();
  const { mutate: removeContributor } = useRemoveContributorMutation();

  const { mutate: createTask } = useCreateTaskMutation();
  const { mutate: updateTask } = useUpdateTaskMutation();
  const { mutate: deleteTask } = useDeleteTaskMutation();

  // Add this function with the other handler functions
  const handleTaskReferenceClick = (task: Task) => {
    if (task.versionId) {
      // Find the version and set it as current
      const targetVersion = project?.versions.find(
        (v) => v.id === task.versionId
      );
      if (targetVersion) {
        setVersion(targetVersion);

        // Set the referenced item for highlighting
        if (task.fileId) {
          setReferencedItemId(task.fileId);
          setReferencedItemType("file");
        } else if (task.folderId) {
          setReferencedItemId(task.folderId);
          setReferencedItemType("folder");
        }
      }
    }
  };

  useEffect(() => {
    if (currentUser) {
      joinProjectRoom(id);
    }

    return () => {
      leaveProjectRoom(id);
    };
  }, [currentUser, id, joinProjectRoom, leaveProjectRoom]);

  useEffect(() => {
    if (project?.versions) {
      setVersion(project.versions[0]);
    }
  }, [project?.versions]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (
    isError ||
    !project ||
    (currentUser &&
      !project.contributors.some(
        (contributor) => Number(contributor.userId) === currentUser.id
      ) &&
      project.author.id !== currentUser.id)
  ) {
    router.push("/");
    return;
  }

  const handlePlayFile = (file: File, url: string, waveform: number[]) => {
    // If there's a current audio URL, revoke it to prevent memory leaks
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }

    // Set the new file and URL
    setCurrentPlayingFile(file);
    setAudioUrl(url);
    setWaveformData(waveform);
    setShowPlayer(true);
  };

  const handleClosePlayer = () => {
    setShowPlayer(false);
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }
    setCurrentPlayingFile(null);
  };

  return (
    <Box className={`relative p-3 ${showPlayer ? "pb-24" : ""}`}>
      <AppBar
        color="transparent"
        className="bg-bgdark rounded-xl mb-3"
        position="static"
      >
        <Toolbar className="bg-bgdark rounded-xl justify-left py-2">
          <ArrowBackIosNew
            className="text-white cursor-pointer"
            onClick={() => router.push("/")}
            sx={{
              padding: "1px",
              mr: 2,
              borderRadius: "50%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              "&:hover": {
                backgroundColor: "rgba(100, 116, 139, 0.4)",
              },
            }}
          />
          <BaseText variant={Fonts.HeadlineSemibold} className="text-slate-300">
            {project?.name}
            <BaseText
              variant={Fonts.FootnoteRegular}
              className="text-slate-400"
            >
              Last modified at{" "}
              {dayjs(project?.updatedAt).format("h:mm A, MMM D, YYYY")}
            </BaseText>
          </BaseText>
          <Box className="ml-auto flex flex-row gap-3 items-center">
            <Textsms
              className="text-white hover:text-slate-400 cursor-pointer "
              sx={{ fontSize: "30px" }}
              onClick={() => {
                if (project.conversations.length === 0) {
                  createProjectConversation(
                    {
                      name: project.name,
                      description:
                        "Project-linked conversation. Edit participants in project contributors.",
                      projectId: project.id,
                      participantIds: project.contributors.map((c) =>
                        Number(c.userId)
                      ),
                    },
                    {
                      onSuccess: (conv) => {
                        queryClient.invalidateQueries({
                          queryKey: [
                            "users",
                            currentUser!.name,
                            "conversations",
                            1,
                            20,
                            "all",
                          ],
                        });
                        router.push(`/chat?cid=${conv.id.toString()}`);
                      },
                      onError: (error) => {
                        console.error("Error creating conversation:", error);
                      },
                    }
                  );
                } else {
                  setTimeout(() => {
                    router.push(
                      `/chat?cid=${project.conversations[0].id.toString()}`
                    );
                  }, 100);
                }
              }}
            />
            {currentUser?.id === project?.author.id && (
              <Settings
                className="text-white hover:text-slate-400 cursor-pointer "
                sx={{ fontSize: "30px" }}
                onClick={() => router.push(`/projects/${project.id}/settings`)}
              />
            )}
          </Box>
        </Toolbar>
      </AppBar>
      <Box className="flex flex-col desktop:flex-row gap-3 w-full">
        <Box className="flex desktop:w-2/3 flex-col rounded-xl text-slate-200 h-full gap-3">
          <ProjectOverview
            project={project}
            currentUser={currentUser}
            inviteContributor={inviteContributor}
            pendingInviteContributor={pendingInviteContributor}
            isInviteContributorModalOpen={isInviteContributorModalOpen}
            setIsInviteContributorModalOpen={setIsInviteContributorModalOpen}
            setIsRemoveContributorModalOpen={setIsRemoveContributorModalOpen}
            setContributorToRemove={setContributorToRemove}
          />
          <ContentsSection
            project={project}
            version={version}
            setVersion={setVersion}
            files={files ?? []}
            filesLoading={filesLoading}
            handleCreateVersionClick={handleCreateVersionClick}
            handleAddFileClick={handleAddFileClick}
            handleUploadFolderClick={handleUploadFolderClick}
            isVersionMessageBeingEdited={isVersionMessageBeingEdited}
            setIsVersionMessageBeingEdited={setIsVersionMessageBeingEdited}
            editedVersionMessage={editedVersionMessage}
            setEditedVersionMessage={setEditedVersionMessage}
            refetchProject={refetchProject}
            versionToDelete={null}
            setVersionToDelete={setVersionToDelete}
            setIsDeleteVersionModalOpen={setIsDeleteVersionModalOpen}
            onPlayFile={handlePlayFile}
            setTargetFolderId={setTargetFolderId}
            setIsAddFileModalOpen={setIsAddFileModalOpen}
            setIsUploadFolderModalOpen={setIsUploadFolderModalOpen}
            referencedItemId={referencedItemId}
            referencedItemType={referencedItemType}
            setReferencedItemId={setReferencedItemId}
            setReferencedItemType={setReferencedItemType}
          />
        </Box>
        <Box className="flex desktop:w-1/3 flex-col rounded-xl text-slate-200 h-full gap-3">
          <CommentsSection
            comments={comments ?? []}
            newComment={newComment}
            setNewComment={setNewComment}
            editingCommentId={editingCommentId}
            setEditingCommentId={setEditingCommentId}
            editedComment={editedComment}
            setEditedComment={setEditedComment}
            createComment={createComment}
            updateComment={updateComment}
            refetchComments={refetchComments}
            currentUser={currentUser}
            projectId={id}
            setCommentToDelete={setCommentToDelete}
            setIsDeleteCommentModalOpen={setIsDeleteCommentModalOpen}
          />
          <TasksSection
            project={project}
            tasks={tasks ?? []}
            handleAddTaskClick={handleAddTaskClick}
            searchedUsers={searchedUsers}
            searchingUsers={searchingUsers}
            handleTaskClick={handleTaskClick}
            handleTaskReferenceClick={handleTaskReferenceClick}
          />
        </Box>
      </Box>

      {/* Confirmation Modal for Creating a New Version */}
      <CreateVersionModal
        isOpen={isConfirmVersionModalOpen}
        onClose={handleConfirmVersionClose}
        nextVersionNumber={nextVersionNumber}
        newVersionMessage={newVersionMessage}
        setNewVersionMessage={setNewVersionMessage}
        createVersion={createVersion}
        isCreatingVersion={isCreatingVersion}
        projectId={id}
        setVersion={setVersion}
        refetchProject={refetchProject}
      />

      {/* Add File Modal */}
      <AddFileModal
        isOpen={isAddFileModalOpen}
        onClose={handleAddFileClose}
        selectedFileName={selectedFileName}
        setSelectedFileName={setSelectedFileName}
        isUploading={isUploading}
        createFile={createFile}
        version={version}
        refetchFiles={refetchFiles}
        targetFolderId={targetFolderId}
        setTargetFolderId={setTargetFolderId}
      />

      {/* Add Folder Modal */}
      <UploadFolderModal
        isOpen={isUploadFolderModalOpen}
        onClose={handleUploadFolderClose}
        version={version}
        targetFolderId={targetFolderId}
        setTargetFolderId={setTargetFolderId}
      />
      <DeleteVersionModal
        isOpen={isDeleteVersionModalOpen}
        onClose={() => setIsDeleteVersionModalOpen(false)}
        versionToDelete={versionToDelete}
        refetchVersions={refetchVersions}
        projectId={id}
      />
      {/* Confirmation Modal for Deleting a Comment */}
      <DeleteCommentModal
        isOpen={isDeleteCommentModalOpen}
        onClose={() => setIsDeleteCommentModalOpen(false)}
        commentToDelete={commentToDelete}
        deleteComment={deleteComment}
        refetchComments={refetchComments}
      />

      {/* Confirmation Modal for Deleting a Task */}
      <DeleteTaskModal
        isOpen={isDeleteTaskModalOpen}
        onClose={() => setIsDeleteTaskModalOpen(false)}
        taskToDelete={taskToDelete}
        deleteTask={deleteTask}
        refetchTasks={refetchTasks}
      />

      {/* Confirmation Modal for Removing a Contributor */}
      <RemoveContributorModal
        isOpen={isRemoveContributorModalOpen}
        onClose={() => setIsRemoveContributorModalOpen(false)}
        removeContributor={removeContributor}
        projectId={id}
        initialContributor={contributorToRemove}
      />
      {/* Audio Player */}
      {showPlayer && audioUrl && currentPlayingFile && (
        <Box className="fixed bottom-0 left-0 right-0 z-50">
          <AudioPlayer
            fileUrl={audioUrl}
            fileName={currentPlayingFile.name}
            waveformData={waveformData}
            onClose={handleClosePlayer}
          />
        </Box>
      )}
      {selectedTask && (
        <TaskModal
          isOpen={isTaskModalOpen}
          onClose={() => setIsTaskModalOpen(false)}
          task={selectedTask}
          editedTaskName={editedTaskName}
          setEditedTaskName={setEditedTaskName}
          editedDueDate={editedDueDate}
          setEditedDueDate={setEditedDueDate}
          editedAssignee={editedAssignee}
          setEditedAssignee={setEditedAssignee}
          setAssigneeQuery={setAssigneeQuery}
          updateTask={updateTask}
          refetchTasks={refetchTasks}
          setTaskToDelete={setTaskToDelete}
          setIsDeleteTaskModalOpen={setIsDeleteTaskModalOpen}
          searchedUsers={searchedUsers}
          searchingUsers={searchingUsers}
          project={project}
        />
      )}
    </Box>
  );
}

const VersionDropdown = ({
  versions,
  version,
  setVersion,
}: {
  versions: Version[] | undefined;
  version: Version | null;
  setVersion: (version: Version | null) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleClick = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("click", handleClick);
    }

    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, [isOpen]);

  return (
    <div className="relative inline-block w-[200px]">
      {isOpen && (
        <div className="absolute top-8 z-10 w-full mt-1 bg-[#21262d] rounded-md shadow-lg">
          {versions?.map((v) => (
            <div
              key={v.number}
              className={`flex gap-1 px-6 py-2 text-gray-200 hover:bg-[#30363d] cursor-pointer ${v.number === version?.number ? "bg-[#30363d]" : ""
                } `}
              onClick={() => {
                setVersion(v);
                setIsOpen(false);
              }}
            >
              <BaseText
                variant={Fonts.SubheadlineSemibold}
                className={
                  v.number === version?.number
                    ? "text-[#948cf0]"
                    : "text-gray-200"
                }
              >
                {v.number}
              </BaseText>
              <BaseText
                variant={Fonts.SubheadlineSemibold}
                className={
                  v.number === version?.number
                    ? "text-[#948cf0]"
                    : "text-gray-300"
                }
              >
                {v.message && ` - ${v.message}`}
              </BaseText>
            </div>
          ))}
        </div>
      )}

      <div
        className={`flex items-center w-full px-2 bg-[#21262d] hover:bg-[#30363d] rounded-lg py-2 cursor-pointer`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-gray-300 text-sm font-semibold ml-4">
          Version:
        </span>
        <span className="text-gray-200 text-sm font-semibold pl-2">
          {versions?.find((v) => v.number === version?.number)?.number ||
            "Select version"}
        </span>
        <svg
          className={`w-4 h-4 text-gray-400 ml-auto transition-transform duration-150 ${isOpen ? "rotate-180" : ""
            }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>
    </div>
  );
};

interface ContentsSectionProps {
  project: Project;
  version: Version | null;
  setVersion: (version: Version | null) => void;
  files: File[];
  filesLoading: boolean;
  handleCreateVersionClick: () => void;
  handleAddFileClick: () => void;
  editedVersionMessage: string;
  setEditedVersionMessage: (value: string) => void;
  isVersionMessageBeingEdited: boolean;
  setIsVersionMessageBeingEdited: (value: boolean) => void;
  versionToDelete: number | null;
  setVersionToDelete: (id: number | null) => void;
  setIsDeleteVersionModalOpen: (isOpen: boolean) => void;
  refetchProject: () => void;
  handleUploadFolderClick: () => void;
  onPlayFile: (file: File, url: string, waveform: number[]) => void;
  setTargetFolderId: (id: number | null) => void;
  setIsAddFileModalOpen: (isOpen: boolean) => void;
  setIsUploadFolderModalOpen: (isOpen: boolean) => void;
  referencedItemId: number | null;
  referencedItemType: "file" | "folder" | null;
  setReferencedItemId: (id: number | null) => void;
  setReferencedItemType: (type: "file" | "folder" | null) => void;
}

const ContentsSection = ({
  project,
  version,
  setVersion,
  files,
  handleCreateVersionClick,
  handleAddFileClick,
  isVersionMessageBeingEdited,
  setIsVersionMessageBeingEdited,
  editedVersionMessage,
  setEditedVersionMessage,
  setVersionToDelete,
  setIsDeleteVersionModalOpen,
  refetchProject,
  handleUploadFolderClick,
  onPlayFile,
  setTargetFolderId,
  setIsAddFileModalOpen,
  setIsUploadFolderModalOpen,
  referencedItemId,
  referencedItemType,
  setReferencedItemId,
  setReferencedItemType,
}: ContentsSectionProps) => {
  const { data: folders } = useGetFoldersByVersionQuery(version?.id ?? 0);
  const { mutate: updateVersion } = useUpdateVersionMutation();
  const { mutate: downloadProject, isPending: isDownloading } =
    useDownloadProjectMutation();

  return (
    <Box
      className="flex flex-col rounded-xl bg-bgdark text-slate-200 h-full p-6 gap-4"
      sx={{
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
      }}
    >
      {/* Version Dropdown and Buttons */}
      <Box className="flex flex-col flex-wrap gap-4">
        <Box className="flex flex-row items-center gap-3 mb-2">
          <Box
            className="p-2 rounded-xl -mt-1"
            sx={{ background: BGStyles.CHROME }}
          >
            <FolderCopy fontSize="medium" />
          </Box>
          <BaseText variant={Fonts.HeadlineSemibold}>Contents</BaseText>
        </Box>
        <Box className="flex flex-row flex-wrap items-center gap-3">
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
            onClick={handleCreateVersionClick}
          >
            <Add fontSize="small" style={{ marginRight: 5 }} /> Create New
            Version
          </Button>
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
            onClick={handleAddFileClick}
          >
            <Add fontSize="small" style={{ marginRight: 5 }} /> Upload File
          </Button>
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
            onClick={handleUploadFolderClick}
          >
            <Add fontSize="small" style={{ marginRight: 5 }} />
            Upload Folder
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={
              isDownloading ? <CircularProgress size={16} /> : <Download />
            }
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
            onClick={() =>
              downloadProject({
                id: Number(project.id),
                projectName: `${project?.name || "project"}-v${version?.number
                  }`,
                versionId: version?.id,
              })
            }
            disabled={isDownloading}
          >
            Download Version
          </Button>
        </Box>
      </Box>
      {/* Version Dropdown */}
      <Box className="flex flex-row gap-3">
        <VersionDropdown
          versions={project?.versions}
          version={version}
          setVersion={setVersion}
        />
        <Box className="bg-bglight px-4 rounded-lg py-1 flex flex-row gap-3 w-full items-center align-middle">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="flex flex-row items-center gap-2"
          >
            <span>
              <Link
                href={"/users/[username]"}
                as={`/users/${version?.author?.name}`}
              >
                <Avatar
                  src={version?.author?.profileImg}
                  sx={{
                    bgcolor: "#4338ca",
                    color: "white",
                    width: "22px",
                    height: "22px",
                    fontSize: "14px",
                    lineHeight: "22px",
                    transition: "filter 0.2s ease-in-out",
                    "&:hover": {
                      filter: "brightness(0.8)",
                    },
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontFamily: "Arial, sans-serif",
                  }}
                >
                  {version?.author?.name.charAt(0).toUpperCase()}
                </Avatar>
              </Link>
            </span>
            {version?.author?.name}
          </BaseText>
          {"\u2022"}
          {isVersionMessageBeingEdited ? (
            <BaseText
              variant={Fonts.SubheadlineSemibold}
              className="text-slate-300 text-[15px]"
            >
              <input
                type="text"
                value={editedVersionMessage}
                onChange={(e) => setEditedVersionMessage(e.target.value)}
                className="w-full px-2 rounded-md bg-[#1f2937] text-slate-300 border 
                  border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
            </BaseText>
          ) : (
            <BaseText
              variant={Fonts.SubheadlineSemibold}
              className="text-slate-300"
            >
              {version?.message || "No message available."}
            </BaseText>
          )}
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className=" text-slate-400 pl-18"
          >
            {dayjs(version?.createdAt).format("h:mm A - MMM D, YYYY")}
          </BaseText>
          <Box className="flex flex-row gap-2 items-center align-middle ml-auto">
            {isVersionMessageBeingEdited ? (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  className="text-sm normal-case mr-2"
                  sx={{
                    backgroundColor: "#4338ca",
                    textTransform: "none",
                    paddingX: 2,
                    paddingY: 1,
                    height: "30px",
                  }}
                  onClick={() => {
                    if (!editedVersionMessage.trim()) {
                      alert("Version message cannot be empty.");
                      return;
                    }
                    if (version?.id) {
                      updateVersion?.(
                        { id: version.id, message: editedVersionMessage },
                        {
                          onSuccess: () => {
                            setIsVersionMessageBeingEdited(false);
                            refetchProject();
                          },
                          onError: (error) => {
                            console.error(
                              "Error updating version message:",
                              error
                            );
                            alert(
                              "Failed to update version message. Please try again."
                            );
                          },
                        }
                      );
                    }
                  }}
                >
                  Save
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  className="text-sm normal-case"
                  sx={{
                    textTransform: "none",
                    paddingX: 2,
                    paddingY: 1,
                    height: "30px",
                  }}
                  onClick={() => setIsVersionMessageBeingEdited(false)}
                >
                  Cancel
                </Button>
              </>
            ) : (
              <>
                <Edit
                  className="text-slate-400 hover:text-slate-500 cursor-pointer"
                  sx={{ fontSize: "25px" }}
                  onClick={() => {
                    setIsVersionMessageBeingEdited(true);
                    setEditedVersionMessage(
                      version?.message || `Version ${version?.number}`
                    );
                  }}
                />
                {version?.number && version?.number > 1 && (
                  <DeleteRounded
                    className="text-slate-400 hover:text-slate-500 cursor-pointer"
                    sx={{ fontSize: "25px" }}
                    onClick={() => {
                      setVersionToDelete(version?.id || null);
                      setIsDeleteVersionModalOpen(true);
                    }}
                  />
                )}
              </>
            )}
          </Box>
        </Box>
      </Box>
      {(project.versions.length === 1 && files?.length === 0 && folders?.length === 0) && (
        <BaseText
          variant={Fonts.BodyRegular}
          className="text-slate-400"
          onClick={handleCreateVersionClick}>For your 1st version, upload your song's stems or final file.
          <br /> They will carry through to the next version.
        </BaseText>
      )}
      {files && files.length > 0 && (
        <FolderTree
          versionId={version?.id as number}
          folders={folders || []}
          files={files}
          onPlayFile={onPlayFile}
          setTargetFolderId={setTargetFolderId}
          setIsAddFileModalOpen={setIsAddFileModalOpen}
          setIsUploadFolderModalOpen={setIsUploadFolderModalOpen}
          referencedItemId={referencedItemId}
          referencedItemType={referencedItemType}
          setReferencedItemId={setReferencedItemId}
          setReferencedItemType={setReferencedItemType}
          versionCount={project?.versions.length || 0}
        />
      )}
    </Box>
  );
};

interface CommentsSectionProps {
  comments: Comment[];
  newComment: string;
  setNewComment: (value: string) => void;
  editingCommentId: number | null;
  setEditingCommentId: (id: number | null) => void;
  editedComment: string;
  setEditedComment: (value: string) => void;
  createComment: UseMutateFunction<
    Comment,
    Error,
    { projectId: number } & CreateCommentBody,
    unknown
  >;
  updateComment: UseMutateFunction<
    Comment,
    Error,
    { id: number } & UpdateCommentBody,
    unknown
  >;
  refetchComments: () => void;
  currentUser: SbUser | null;
  projectId: number;
  setCommentToDelete: (id: number | null) => void;
  setIsDeleteCommentModalOpen: (value: boolean) => void;
}

const CommentsSection = ({
  comments,
  newComment,
  setNewComment,
  editingCommentId,
  setEditingCommentId,
  editedComment,
  setEditedComment,
  createComment,
  updateComment,
  refetchComments,
  currentUser,
  projectId,
  setCommentToDelete,
  setIsDeleteCommentModalOpen,
}: CommentsSectionProps) => {
  return (
    <Box
      className="flex w-full flex-col rounded-xl bg-bgdark text-slate-200 h-full p-6 gap-4"
      sx={{
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
      }}
    >
      <Box className="flex flex-row items-center gap-3 mb-2">
        <Box
          className="p-2 rounded-xl -mt-1"
          sx={{ background: BGStyles.CHROME }}
        >
          <Chat fontSize="medium" />
        </Box>
        <BaseText variant={Fonts.HeadlineSemibold}>Comments</BaseText>
      </Box>
      <Box className="bg-bglight p-4 rounded-lg flex flex-col gap-4">
        {/* Input for adding a new comment */}
        <Box className="flex flex-row items-center gap-4">
          <input
            type="text"
            placeholder="Write a comment..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            className="flex-1 p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
            onClick={() => {
              if (!newComment.trim()) {
                alert("Comment cannot be empty.");
                return;
              }

              createComment(
                { projectId, content: newComment },
                {
                  onSuccess: () => {
                    setNewComment(""); // Clear the input field
                    refetchComments(); // Refetch comments to update the list
                  },
                  onError: (error) => {
                    console.error("Error creating comment:", error);
                    alert("Failed to add comment. Please try again.");
                  },
                }
              );
            }}
          >
            Post
          </Button>
        </Box>
      </Box>
      <Box className="flex flex-col gap-4 max-h-60 overflow-y-auto">
        {/* Placeholder for comments */}
        {comments && comments.length > 0 ? (
          comments.map((comment, index) => (
            <Box key={index} className="flex flex-row gap-4 w-full pr-2">
              <Link
                href="/users/[username]"
                as={`/users/${comment.author.name}`}
              >
                <Avatar
                  src={comment.author.profileImg}
                  sx={{
                    bgcolor: "#4338ca",
                    color: "white",
                    width: "36px",
                    height: "36px",
                    fontSize: "16px",
                  }}
                >
                  {comment.author.name.charAt(0).toUpperCase()}
                </Avatar>
              </Link>
              <Box className="bg-bglight px-4 py-3 rounded-lg text-slate-300 flex flex-col gap-2 w-full">
                <Box className="flex flex-row justify-between">
                  <BaseText
                    variant={Fonts.FootnoteRegular}
                    className="text-slate-400 flex items-center gap-2"
                  >
                    {comment.author.name} at{" "}
                    {dayjs(comment.createdAt).format("h:mm A - MMM D, YYYY")}
                    <span className="italic">
                      {comment.createdAt !== comment.updatedAt && "(Edited)"}
                    </span>
                  </BaseText>
                  {comment.authorId === Number(currentUser?.id) && (
                    <Box className="flex flex-row gap-2">
                      {editingCommentId === comment.id ? (
                        <>
                          <Button
                            variant="contained"
                            color="primary"
                            className="text-sm normal-case mr-2"
                            sx={buttonStyles.primaryButtonStyles}
                            onClick={() => {
                              if (!editedComment.trim()) {
                                alert("Comment cannot be empty.");
                                return;
                              }
                              updateComment(
                                { id: comment.id, content: editedComment },
                                {
                                  onSuccess: () => {
                                    setEditingCommentId(null); // Exit edit mode
                                    refetchComments(); // Refetch comments to update the list
                                  },
                                  onError: (error) => {
                                    console.error(
                                      "Error updating comment:",
                                      error
                                    );
                                    alert(
                                      "Failed to update comment. Please try again."
                                    );
                                  },
                                }
                              );
                            }}
                          >
                            Confirm
                          </Button>
                          <Button
                            variant="outlined"
                            className="text-sm normal-case"
                            sx={buttonStyles.outlinedDangerButtonStyles}
                            onClick={() => setEditingCommentId(null)} // Exit edit mode
                          >
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <>
                          <Edit
                            className="text-slate-400 hover:text-slate-500 cursor-pointer"
                            fontSize="small"
                            onClick={() => {
                              setEditingCommentId(comment.id); // Enter edit mode
                              setEditedComment(comment.content); // Pre-fill the input with the current comment content
                            }}
                          />
                          <DeleteRounded
                            className="text-slate-400 hover:text-slate-500 cursor-pointer ml-2"
                            fontSize="small"
                            onClick={() => {
                              setCommentToDelete(comment.id);
                              setIsDeleteCommentModalOpen(true);
                            }}
                          />
                        </>
                      )}
                    </Box>
                  )}
                </Box>
                {editingCommentId === comment.id ? (
                  <input
                    type="text"
                    value={editedComment}
                    onChange={(e) => setEditedComment(e.target.value)}
                    className="w-full p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <BaseText variant={Fonts.BodyRegular}>
                    {comment.content}
                  </BaseText>
                )}
              </Box>
            </Box>
          ))
        ) : (
          <BaseText
            variant={Fonts.BodyRegular}
            className="bg-bglight p-4 rounded-lg text-slate-400"
          >
            No comments yet! Be the first to add one.
          </BaseText>
        )}
      </Box>
    </Box>
  );
};

const ProjectOverview = ({
  project,
  currentUser,
  isInviteContributorModalOpen,
  setIsInviteContributorModalOpen,
  inviteContributor,
  pendingInviteContributor,
  setIsRemoveContributorModalOpen,
  setContributorToRemove,
}: {
  project: Project;
  currentUser: SbUser | null;
  isInviteContributorModalOpen: boolean;
  setIsInviteContributorModalOpen: (value: boolean) => void;
  inviteContributor: UseMutateFunction<
    ProjectInvite,
    Error,
    { userId: number; projectId: number },
    unknown
  >;
  pendingInviteContributor: boolean;
  setIsRemoveContributorModalOpen: (value: boolean) => void;
  setContributorToRemove: (contributor: ProjectUser | null) => void;
}) => {
  const { data: projectInvites, refetch: refetchProjectInvites } =
    useGetProjectInvitesQuery(project.id, {
      enabled: currentUser?.id === project.author.id,
    });
  const pendingInvites =
    projectInvites?.filter((invite) => invite.status === "pending") || [];

  const { mutate: revokeProjectInvite } = useRevokeProjectInviteMutation();

  return (
    <Box
      className="flex flex-col rounded-xl bg-bgdark text-slate-200 w-full h-full p-6 gap-4"
      sx={{
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
      }}
    >
      <Box className="flex flex-row items-center gap-3 mb-2">
        <Box
          className="p-2 rounded-xl -mt-1"
          sx={{ background: BGStyles.CHROME }}
        >
          <Lightbulb fontSize="medium" />
        </Box>
        <BaseText variant={Fonts.HeadlineSemibold}>Overview</BaseText>
      </Box>

      {/* Stats Section - Horizontal Layout */}
      <Box className="flex justify-between bg-bglight p-4 rounded-lg">
        <Box className="flex flex-col items-center flex-1">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400"
          >
            Author
          </BaseText>
          <BaseText
            variant={Fonts.BodySemibold}
            className="flex flex-row items-center gap-2"
          >
            <span>
              <Link
                href="/users/[username]"
                as={`/users/${project?.author.name}`}
              >
                <Avatar
                  src={project?.author.profileImg}
                  sx={{
                    bgcolor: "#4338ca",
                    color: "white",
                    width: "22px",
                    height: "22px",
                    fontSize: "14px",
                  }}
                  popover="auto"
                >
                  {project?.author.name.charAt(0).toUpperCase()}
                </Avatar>
              </Link>
            </span>
            {project?.author.name}
          </BaseText>
        </Box>
        <Box className="flex flex-col items-center flex-1">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400"
          >
            Genre
          </BaseText>
          <BaseText variant={Fonts.BodySemibold}>{project?.genre}</BaseText>
        </Box>
        <Box className="flex flex-col items-center flex-1">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400"
          >
            Tempo
          </BaseText>
          <BaseText
            variant={Fonts.BodySemibold}
            className={`${project.tempo ? "text-slate-300" : " text-slate-400"
              }`}
          >
            {project?.tempo ? `${project?.tempo} bpm` : "--"}
          </BaseText>
        </Box>
        <Box className="flex flex-col items-center flex-1">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400"
          >
            Key
          </BaseText>
          <BaseText
            variant={Fonts.BodySemibold}
            className={`${project.key ? "text-slate-300" : " text-slate-400"}`}
          >
            {project?.key || "--"}
          </BaseText>
        </Box>
        <Box className="flex flex-col items-center flex-1">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400"
          >
            Created at
          </BaseText>
          <BaseText variant={Fonts.BodySemibold}>
            {dayjs(project?.createdAt).format("MMM D, YYYY")}
          </BaseText>
        </Box>
        <Box className="flex flex-col items-center flex-1">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400"
          >
            Updated at
          </BaseText>
          <BaseText variant={Fonts.BodySemibold}>
            {dayjs(project?.updatedAt).format("MMM D, YYYY")}
          </BaseText>
        </Box>
      </Box>

      {/* Description Section */}
      <Box className="bg-bglight p-6 rounded-lg flex flex-row">
        <Box className="w-2/3">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400 mb-4"
          >
            Description
          </BaseText>
          <BaseText
            variant={Fonts.BodyRegular}
            className={`${project.description ? "text-slate-300" : " text-slate-400"
              }`}
          >
            {project?.description || "--"}
          </BaseText>
        </Box>
        <Box className="w-1/3">
          <BaseText
            variant={Fonts.SubheadlineRegular}
            className="text-slate-400 mb-4"
          >
            Contributors
          </BaseText>
          <Box className="text-slate-300 flex flex-row flex-wrap gap-3">
            {currentUser?.id === project.author.id && (
              <Add
                className="cursor-pointer hover:text-slate-500"
                onClick={() => setIsInviteContributorModalOpen(true)} // Open the modal
              />
            )}

            {/* Avatars with names */}
            {project?.contributors.map((contributor, index) => (
              <BaseText
                key={index}
                variant={Fonts.BodyRegular}
                className="flex flex-row items-center gap-2"
              >
                <span>
                  <Link
                    href={"/users/[username]"}
                    as={`/users/${contributor.user?.name}`}
                  >
                    <Avatar
                      src={contributor.user?.profileImg}
                      sx={{
                        bgcolor: "#4338ca", // This will be randomized later
                        color: "white",
                        width: "22px",
                        height: "22px",
                        fontSize: "14px",
                        transition: "filter 0.2s ease-in-out", // Smooth transition
                        "&:hover": {
                          filter: "brightness(0.8)", // Darkens the avatar on hover
                        },
                      }}
                      popover="auto"
                    >
                      {contributor.user?.name.charAt(0).toUpperCase()}
                    </Avatar>
                  </Link>
                </span>
                {contributor.user?.name}
                {((currentUser?.id === project.author.id &&
                  contributor.user?.id !== project.author.id) ||
                  (currentUser?.id !== project.author.id &&
                    currentUser?.id === contributor.user?.id)) && (
                    <DeleteRounded
                      className="text-slate-400 hover:text-slate-500 cursor-pointer"
                      fontSize="small"
                      onClick={() => {
                        setContributorToRemove(contributor);
                        setIsRemoveContributorModalOpen(true);
                      }}
                    />
                  )}
              </BaseText>
            ))}

            {/* Pending Invites */}
            {project.author.id === currentUser?.id &&
              pendingInvites?.map((invite) => (
                <BaseText
                  key={invite.id}
                  variant={Fonts.BodyRegular}
                  className="flex flex-row items-center gap-2 text-slate-500 italic"
                >
                  <span>
                    <Avatar
                      src={invite.user?.profileImg}
                      sx={{
                        bgcolor: "#64748b",
                        color: "white",
                        width: "22px",
                        height: "22px",
                        fontSize: "14px",
                      }}
                      popover="auto"
                    >
                      {invite.user?.name.charAt(0).toUpperCase()}
                    </Avatar>
                  </span>
                  {invite.user?.name}
                  {currentUser?.id === project.author.id && (
                    <DeleteRounded
                      className="text-slate-500 hover:text-slate-400 cursor-pointer italic"
                      fontSize="small"
                      onClick={() => {
                        revokeProjectInvite(invite.id, {
                          onSuccess: () => {
                            refetchProjectInvites();
                          },
                        });
                      }}
                    />
                  )}
                </BaseText>
              ))}
          </Box>

          {/* Invite Contributor Modal */}
          <InviteContributorModal
            isOpen={isInviteContributorModalOpen}
            onClose={() => setIsInviteContributorModalOpen(false)}
            inviteContributor={inviteContributor}
            pendingInviteContributor={pendingInviteContributor}
            projectId={project.id}
          />
        </Box>
      </Box>
    </Box>
  );
};

interface CreateVersionModalProps {
  isOpen: boolean;
  onClose: () => void;
  nextVersionNumber: number | null;
  newVersionMessage: string;
  setNewVersionMessage: (value: string) => void;
  createVersion: UseMutateFunction<Version, Error, CreateVersionBody, unknown>; // Replace `any` with the appropriate mutation type
  isCreatingVersion: boolean;
  projectId: number;
  setVersion: (version: Version) => void;
  refetchProject: () => void;
}

const CreateVersionModal = ({
  isOpen,
  onClose,
  nextVersionNumber,
  newVersionMessage,
  setNewVersionMessage,
  createVersion,
  isCreatingVersion,
  projectId,
  setVersion,
  refetchProject,
}: CreateVersionModalProps) => {
  const router = useRouter();
  return (
    <Modal title="Create New Version?" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4 mt-3 text-center">
        Are you sure you want to create version {nextVersionNumber}?
      </BaseText>
      <Box className="flex flex-col gap-4">
        {/* Input for the optional message */}
        <input
          type="text"
          placeholder="Enter version message (optional)"
          value={newVersionMessage}
          onChange={(e) => setNewVersionMessage(e.target.value)}
          className="w-full text-center p-2 rounded-md bg-bglight text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </Box>
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={() => {
            createVersion(
              {
                projectId,
                message: newVersionMessage || `Version ${nextVersionNumber}`,
              },
              {
                onSuccess: (newVersion) => {
                  onClose();
                  setVersion(newVersion);
                  setNewVersionMessage("");
                  router.refresh();
                  refetchProject();
                },
                onError: (error) => {
                  console.error("Error creating version:", error);
                  alert("Failed to create version. Please try again.");
                },
              }
            );
          }}
        >
          {isCreatingVersion ? (
            <CircularProgress size={16} color="inherit" />
          ) : (
            "Confirm"
          )}
        </Button>
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

interface AddFileModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedFileName: string | null;
  setSelectedFileName: (value: string | null) => void;
  isUploading: boolean;
  createFile: UseMutateFunction<File, Error, FormData, unknown>; // Replace `any` with the appropriate mutation type
  version: Version | null;
  refetchFiles: () => void;
  targetFolderId: number | null;
  setTargetFolderId: (id: number | null) => void;
}

const AddFileModal = ({
  isOpen,
  onClose,
  selectedFileName,
  setSelectedFileName,
  isUploading,
  createFile,
  version,
  refetchFiles,
  targetFolderId,
  setTargetFolderId,
}: AddFileModalProps) => {
  const { data: targetFolder } = useGetFolderQuery(targetFolderId || 0);
  // Inside your AddFileModal component
  const { uploads } = useUploadProgress();

  return (
    <Modal title="Upload Files" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4">
        Add a new file to{" "}
        {targetFolder ? `"${targetFolder.name}"` : "this version"}
      </BaseText>
      <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
        You can add files to this version. Please ensure the file is in a
        supported format.
      </BaseText>
      <Box className="flex flex-col gap-4 mt-4">
        <Box className="flex flex-row items-center gap-4">
          <Button
            variant="contained"
            component="label"
            color="primary"
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
          >
            Choose File
            <input
              type="file"
              accept={FINAL_ALLOWED_EXTENSIONS.join(",")}
              hidden
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  setSelectedFileName(file.name);
                }
              }}
            />
          </Button>
          <BaseText variant={Fonts.BodyRegular} className="text-slate-300">
            {selectedFileName ? (
              <div className="flex flex-row">
                <span className="font-bold text-slate-200 ">Selected:</span>
                <span className="inline-flex items-center ml-1">
                  <InsertDriveFile className="text-blue-300 ml-1 mr-1" />
                  <span className="font-semibold text-slate-200">
                    {selectedFileName}
                  </span>
                </span>
              </div>
            ) : (
              "No folder chosen"
            )}
          </BaseText>
        </Box>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          disabled={isUploading || !selectedFileName}
          sx={buttonStyles.primaryButtonStyles}
          onClick={() => {
            const fileInput = document.querySelector(
              'input[type="file"]'
            ) as HTMLInputElement;
            const file = fileInput?.files?.[0];

            if (!file || !version?.id) {
              alert(
                file
                  ? "No version selected."
                  : "Please select a file to upload."
              );
              return;
            }

            const formData = new FormData();
            formData.append("file", file);
            formData.append("name", file.name);
            formData.append("versionId", version.id.toString());
            formData.append("fileType", file.type);
            formData.append("fileSize", file.size.toString());
            if (targetFolderId) {
              formData.append("folderId", targetFolderId.toString());
              formData.append("independent", "true");
            }

            createFile(formData, {
              onSuccess: () => {
                setSelectedFileName(null);
                setTargetFolderId(null);
                onClose();
                refetchFiles();
              },
              onError: (error) => {
                console.error("Error uploading file:", error);
                alert("Failed to upload file. Please try again.");
                setTargetFolderId(null);
              },
            });
          }}
        >
          {isUploading ? "Uploading..." : "Upload File"}
        </Button>
        {Object.values(uploads).map((upload) => (
          <UploadProgressBar
            key={upload.id}
            fileName={upload.fileName}
            progress={upload.progress}
            status={upload.status}
          />
        ))}
      </Box>
    </Modal>
  );
};

interface DeleteCommentModalProps {
  isOpen: boolean;
  onClose: () => void;
  commentToDelete: number | null;
  deleteComment: UseMutateFunction<
    { success: boolean },
    Error,
    number,
    unknown
  >; // Replace `any` with the appropriate mutation type
  refetchComments: () => void;
}

const DeleteCommentModal = ({
  isOpen,
  onClose,
  commentToDelete,
  deleteComment,
  refetchComments,
}: DeleteCommentModalProps) => {
  return (
    <Modal title="Delete Comment?" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4 mt-3 text-center">
        Are you sure you want to delete this comment?
      </BaseText>
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          onClick={() => {
            if (commentToDelete === null) return;

            deleteComment(commentToDelete, {
              onSuccess: () => {
                onClose();
                refetchComments();
              },
              onError: (error) => {
                console.error("Error deleting comment:", error);
                alert("Failed to delete comment. Please try again.");
              },
            });
          }}
        >
          Confirm
        </Button>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

interface DeleteVersionModalProps {
  isOpen: boolean;
  onClose: () => void;
  versionToDelete: number | null;
  refetchVersions: () => void;
  projectId: number;
}
const DeleteVersionModal = ({
  isOpen,
  onClose,
  versionToDelete,
  refetchVersions,
  projectId,
}: DeleteVersionModalProps) => {
  const { mutate: deleteVersion } = useDeleteVersionMutation();
  return (
    <Modal title="Delete Version?" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4 mt-3 text-center">
        Are you sure you want to delete this version?
      </BaseText>
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          onClick={() => {
            if (versionToDelete === null) return;

            deleteVersion(
              { id: versionToDelete, projectId },
              {
                onSuccess: () => {
                  onClose();
                  refetchVersions();
                },
                onError: (error) => {
                  console.error("Error deleting version:", error);
                  alert("Failed to delete version. Please try again.");
                },
              }
            );
          }}
        >
          Confirm
        </Button>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

const DeleteTaskModal = ({
  isOpen,
  onClose,
  taskToDelete,
  deleteTask,
  refetchTasks,
}: {
  isOpen: boolean;
  onClose: () => void;
  taskToDelete: number | null;
  deleteTask: UseMutateFunction<{ success: boolean }, Error, number, unknown>;
  refetchTasks: () => void;
}) => {
  return (
    <Modal title="Delete Task?" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4 mt-3 text-center">
        Are you sure you want to delete this task?
      </BaseText>
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          onClick={() => {
            if (taskToDelete === null) return;

            deleteTask(taskToDelete, {
              onSuccess: () => {
                onClose();
                refetchTasks();
              },
              onError: (error) => {
                console.error("Error deleting task:", error);
                alert("Failed to delete task. Please try again.");
              },
            });
          }}
        >
          Confirm
        </Button>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};

interface TasksSectionProps {
  project: Project;
  tasks: Task[];
  handleAddTaskClick: () => void;
  searchedUsers: User[] | undefined;
  searchingUsers: boolean;
  handleTaskClick: (task: Task) => void;
  handleTaskReferenceClick: (task: Task) => void;
}

const TasksSection = ({
  tasks,
  handleTaskClick,
  handleAddTaskClick,
  handleTaskReferenceClick,
}: TasksSectionProps) => {
  return (
    <Box
      className="flex w-full flex-col rounded-xl bg-bgdark text-slate-200 h-full p-6 gap-4"
      sx={{
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.3) !important",
      }}
    >
      {/* Tasks Header */}
      <Box className="flex flex-row justify-between items-center">
        <Box className="flex flex-row items-center gap-3 mb-2">
          <Box
            className="p-2 rounded-xl -mt-1"
            sx={{ background: BGStyles.CHROME }}
          >
            <TaskSharp fontSize="medium" />
          </Box>
          <BaseText variant={Fonts.HeadlineSemibold}>Tasks</BaseText>
        </Box>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={handleAddTaskClick}
        >
          <Add className="mr-2 -ml-1" /> Add Task
        </Button>
      </Box>

      {/* Tasks Table Header */}
      <Box className="grid grid-cols-[1.5fr_1fr_1fr_1fr] gap-5 mx-6">
        <BaseText
          variant={Fonts.BodyRegular}
          className="text-slate-400 text-center"
        >
          Name
        </BaseText>
        <BaseText
          variant={Fonts.BodyRegular}
          className="text-slate-400 text-center"
        >
          Assigned to
        </BaseText>
        <BaseText
          variant={Fonts.BodyRegular}
          className="text-slate-400 text-center"
        >
          Status
        </BaseText>
        <BaseText
          variant={Fonts.BodyRegular}
          className="text-slate-400 text-center"
        >
          Due by
        </BaseText>
      </Box>

      {/* Tasks List */}
      <Box className="bg-bglight p-4 rounded-lg flex-col flex gap-1 -mt-1 max-h-80 overflow-auto pt-3">
        {tasks && tasks.length > 0 ? (
          tasks.map((task) => (
            <Box
              key={task.id}
              className="grid grid-cols-[1.5fr_1fr_1fr_1fr] gap-4 py-2 px-2 items-center relative border-b-[1px] border-slate-700 cursor-pointer hover:bg-[#1a2234] hover:rounded-lg transition-all group"
            >
              {/* Overlay "Edit" text on hover */}
              <Box className="absolute flex flex-row text-center inset-0 bg-bgdark bg-opacity-50 items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-lg z-10 backdrop-blur-sm">
                <Box
                  className="flex-1 h-full flex items-center justify-center text-slate-400 hover:text-slate-200"
                  onClick={() => handleTaskClick(task)}
                >
                  <BaseText variant={Fonts.SubheadlineSemibold}>Edit</BaseText>
                </Box>
                {(task.versionId || task.folderId || task.fileId) && (
                  <Box
                    className="flex-1 h-full flex items-center justify-center text-slate-400 hover:text-slate-200"
                    onClick={() => handleTaskReferenceClick(task)}
                  >
                    <BaseText variant={Fonts.SubheadlineSemibold}>
                      Reference
                    </BaseText>
                  </Box>
                )}
              </Box>

              {/* Task Name */}
              <Box className="text-center overflow-hidden">
                <div className="w-full overflow-hidden px-2">
                  <BaseText
                    variant={Fonts.SubheadlineSemibold}
                    className="text-slate-300 cursor-pointer truncate block w-full"
                    title={task.title}
                  >
                    {task.title}
                  </BaseText>
                </div>
              </Box>

              {/* Task Assignee */}
              <Box className="text-center">
                <Box className="flex items-center gap-2 cursor-pointer justify-center">
                  {task.assignee && task.assignee.name !== "Everyone" && (
                    <Link
                      href={"/users/[username]"}
                      as={`/users/${task.assignee.name}`}
                    >
                      <Avatar
                        src={task.assignee.profileImg}
                        sx={{
                          bgcolor: "#4338ca",
                          color: "white",
                          width: "22px",
                          height: "22px",
                          fontSize: "14px",
                          transition: "filter 0.2s ease-in-out",
                          "&:hover": {
                            filter: "brightness(0.8)",
                          },
                        }}
                      >
                        {task.assignee.name.charAt(0).toUpperCase()}
                      </Avatar>
                    </Link>
                  )}
                  <BaseText
                    variant={Fonts.SubheadlineRegular}
                    className="text-slate-400"
                  >
                    {task.assignee?.name || "Everyone"}
                  </BaseText>
                </Box>
              </Box>

              {/* Task Status */}
              <Box className="text-center flex items-center justify-center">
                <BaseText
                  variant={Fonts.SubheadlineRegular}
                  className="cursor-pointer text-[14px]"
                  sx={{
                    color: statusColors[task.status as TaskStatus],
                    fontSize: "14px",
                  }}
                >
                  {taskStatusDisplayNames[task.status as TaskStatus]}
                </BaseText>
              </Box>

              {/* Task Due Date */}
              <Box className="text-center">
                <BaseText
                  variant={Fonts.SubheadlineRegular}
                  className="text-slate-400"
                >
                  {task.dueDate
                    ? dayjs(task.dueDate).format("MMM D, YYYY")
                    : "--"}
                </BaseText>
              </Box>
            </Box>
          ))
        ) : (
          <BaseText
            variant={Fonts.BodyRegular}
            className="text-slate-400 text-center"
          >
            No tasks yet!
          </BaseText>
        )}
      </Box>
    </Box>
  );
};

const InviteContributorModal = ({
  isOpen,
  onClose,
  inviteContributor,
  pendingInviteContributor,
  projectId,
}: {
  isOpen: boolean;
  onClose: () => void;
  inviteContributor: UseMutateFunction<
    ProjectInvite,
    Error,
    { userId: number; projectId: number },
    unknown
  >;
  pendingInviteContributor: boolean;
  projectId: number;
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<{
    id: number;
    name: string;
  } | null>(null);
  const { data: users, isLoading } = useSearchUsersQuery(searchQuery);

  const handleUserSelect = (user: { id: number; name: string }) => {
    setSelectedUser(user);
    setSearchQuery(user.name);
  };

  const handleSubmit = () => {
    if (!selectedUser) {
      alert("Please select a user from the dropdown.");
      return;
    }

    inviteContributor(
      { projectId: projectId, userId: selectedUser.id },
      {
        onSuccess: () => {
          setSearchQuery(""); // Clear the input field
          setSelectedUser(null);
          onClose(); // Close the modal
        },
        onError: (error) => {
          console.error("Error inviting contributor:", error);
          alert("Failed to invite contributor. Please try again.");
        },
      }
    );
  };

  return (
    <Modal title="Invite Contributor" isOpen={isOpen} onClose={onClose}>
      <BaseText variant={Fonts.BodySemibold} className="mb-4">
        Search for a user to invite:
      </BaseText>
      <Box className="flex flex-col gap-4 min-w-[400px]">
        <Box className="relative">
          <input
            type="text"
            placeholder="Search username..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setSelectedUser(null);
            }}
            className="w-full p-2 rounded-md bg-[#1f2937] text-slate-300 border border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />

          {/* Dropdown */}
          {searchQuery.length >= 2 && !selectedUser && (
            <Box className="absolute w-full mt-1 bg-[#1f2937] border border-slate-600 rounded-md shadow-lg z-50 max-h-48 overflow-y-auto">
              {isLoading ? (
                <Box className="p-2 text-slate-400">Searching...</Box>
              ) : users && users.length > 0 ? (
                users.map((user: User) => (
                  <Box
                    key={user.id}
                    className="p-2 hover:bg-slate-700 cursor-pointer text-slate-300"
                    onClick={() => handleUserSelect(user)}
                  >
                    <Box className="flex items-center gap-2">
                      <Avatar
                        src={user.profileImg}
                        sx={{
                          bgcolor: "#4338ca",
                          color: "white",
                          width: "24px",
                          height: "24px",
                          fontSize: "12px",
                        }}
                      >
                        {user.name.charAt(0).toUpperCase()}
                      </Avatar>
                      <BaseText variant={Fonts.BodySemibold} className="mr-2">
                        {user.name}
                      </BaseText>
                      <BaseText
                        variant={Fonts.BodyRegular}
                        className="text-slate-400"
                      >
                        {user.email}
                      </BaseText>
                    </Box>
                  </Box>
                ))
              ) : (
                <Box className="p-2 text-slate-400">No users found</Box>
              )}
            </Box>
          )}
        </Box>

        {pendingInviteContributor ? (
          <CircularProgress
            size={24}
            color="inherit"
            className="self-center flex mx-auto"
          />
        ) : (
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={{
              backgroundColor: "#4338ca",
              textTransform: "none",
              fontSize: "16px",
            }}
            disabled={!selectedUser || pendingInviteContributor}
            onClick={handleSubmit}
          >
            Invite Contributor
          </Button>
        )}
      </Box>
    </Modal>
  );
};

const RemoveContributorModal = ({
  isOpen,
  onClose,
  removeContributor,
  projectId,
  initialContributor,
}: {
  isOpen: boolean;
  onClose: () => void;
  removeContributor: UseMutateFunction<
    { success: boolean },
    Error,
    { id: number; userName: string },
    unknown
  >;
  projectId: number;
  initialContributor: ProjectUser | null;
}) => {
  const router = useRouter();
  const [contributorToRemove, setContributorToRemove] =
    useState<ProjectUser | null>(initialContributor);

  const { user: currentUser } = useAuth();

  useEffect(() => {
    if (isOpen) {
      setContributorToRemove(initialContributor); // Set the contributor when the modal opens
    } else {
      setContributorToRemove(null); // Reset the state when the modal closes
    }
  }, [isOpen, initialContributor]);

  return (
    <Modal title="Remove Contributor" isOpen={isOpen} onClose={onClose}>
      {currentUser?.id === contributorToRemove?.user.id ? (
        <BaseText variant={Fonts.BodySemibold} className="mb-4">
          Are you sure you want to leave this project?
        </BaseText>
      ) : (
        <BaseText variant={Fonts.BodySemibold} className="mb-4">
          Are you sure you want to remove{" "}
          <span className="text-red-500">
            {contributorToRemove?.user?.name}
          </span>{" "}
          from this project?
        </BaseText>
      )}
      <Box className="flex flex-row gap-4 justify-center mt-4">
        <Button
          variant="outlined"
          color="secondary"
          className="text-md normal-case"
          sx={buttonStyles.outlinedDangerButtonStyles}
          onClick={() => {
            if (!contributorToRemove) return;

            removeContributor(
              { id: projectId, userName: contributorToRemove.user.name },
              {
                onSuccess: () => {
                  // If you removed yourself, redirect to the projects page
                  if (contributorToRemove.user.id === currentUser?.id) {
                    router.push("/projects");
                  }
                  onClose();
                },
                onError: (error) => {
                  console.error("Error removing contributor:", error);
                  alert("Failed to remove contributor. Please try again.");
                },
              }
            );
          }}
        >
          Confirm
        </Button>
        <Button
          variant="contained"
          color="primary"
          className="text-md normal-case"
          sx={buttonStyles.primaryButtonStyles}
          onClick={onClose}
        >
          Cancel
        </Button>
      </Box>
    </Modal>
  );
};
