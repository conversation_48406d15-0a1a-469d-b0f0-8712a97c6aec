"use client";
import { BaseText } from "@/components/base/BaseText";
import Modal from "@/components/base/Modal";
import { Fonts } from "@/constants/typology";
import { useAuth } from "@/contexts/AuthContext";
import { Conversation, ConversationParticipant, Message } from "@/models/chat";
import {
  useCreateConversationMutation,
  useCreateMessageMutation,
  useDeleteConversationMutation,
  useDeleteMessageMutation,
  useGetConversationQuery,
  useUpdateConversationMutation,
  useUpdateMessageMutation,
} from "@/routes/chat";
import {
  Add,
  ChatBubbleOutline,
  Close,
  Delete,
  Edit,
  LinkRounded,
  Send,
} from "@mui/icons-material";
import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import dayjs from "dayjs";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { BGStyles } from "../../../constants/colors";
import { User } from "../../../models/user";
import {
  useGetUserConversationsQuery,
  useSearchUsersQuery,
} from "../../../routes/user";
import { buttonStyles } from "../../../utils/buttonStyles";

export default function ChatPage() {
  const { user } = useAuth();
  // use url queries
  const searchParams = useSearchParams();
  const cid = searchParams.get("cid") || "";
  const queryClient = useQueryClient();

  const [filter, setFilter] = useState<"linked" | "created" | "all">("all");

  const [page, setPage] = useState(1);
  const [conversations, setConversations] = useState<Conversation[]>([]);

  const [messagePage, setMessagePage] = useState(1);
  const [allMessages, setAllMessages] = useState<Message[]>([]);

  // Simplified refs
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const conversationListRef = useRef<HTMLDivElement>(null);

  // Get conversations for the current user
  const {
    data: conversationsData,
    isLoading: loadingConvs,
    refetch: refetchConvs,
  } = useGetUserConversationsQuery(user?.name || "", page, 20, filter);

  // Infinite scroll hook
  const useInfiniteScroll = (
    containerRef: React.RefObject<HTMLElement>,
    hasMore: boolean,
    isLoading: boolean,
    loadMore: () => void
  ) => {
    const [isFetching, setIsFetching] = useState(false);

    useEffect(() => {
      const handleScroll = () => {
        setIsFetching(false); // Reset fetching state on scroll
        const container = containerRef.current;
        if (!container || !hasMore || isLoading || isFetching) return;
        const { scrollTop, scrollHeight, clientHeight } = container;
        const threshold = 5; // Load when 5px from bottom

        if (scrollTop + clientHeight >= scrollHeight - threshold) {
          setIsFetching(true);
          loadMore();
        }
      };

      const container = containerRef.current;
      if (container) {
        container.addEventListener("scroll", handleScroll);
        return () => container.removeEventListener("scroll", handleScroll);
      }
    }, [hasMore, isLoading, isFetching]);

    return isFetching;
  };

  const loadMore = useCallback(() => {
    setPage((prev) => prev + 1);
  }, []);

  // Use infinite scroll
  const isFetching = useInfiniteScroll(
    conversationListRef,
    conversationsData?.hasMore || false,
    loadingConvs,
    loadMore
  );

  // Handle conversation data updates - simplified
  useEffect(() => {
    if (conversationsData?.conversations) {
      if (page === 1) {
        // Force update even if data looks the same
        setConversations([...conversationsData.conversations]);
      } else {
        // For infinite scroll, append new conversations
        setConversations((prev) => {
          const existingIds = new Set(prev.map((conv) => conv.id));
          const newConversations = conversationsData.conversations.filter(
            (conv) => !existingIds.has(conv.id)
          );
          return [...prev, ...newConversations];
        });
      }
    }
    // Add this condition to handle empty responses
    else if (
      conversationsData?.conversations &&
      conversationsData.conversations.length === 0
    ) {
      setConversations([]);
    }
  }, [conversationsData?.conversations, page]);

  useEffect(() => {
    // Reset conversations when filter changes or on initial load
    if (page === 1) {
      setConversations([]);
    }
  }, [filter, page]);

  useEffect(() => {
    // This handles the case where conversationsData is available but conversations state is empty
    if (
      conversationsData?.conversations &&
      conversations.length === 0 &&
      page === 1 &&
      !loadingConvs
    ) {
      setConversations([...conversationsData.conversations]);
    }
  }, [
    conversationsData?.conversations,
    conversations.length,
    page,
    loadingConvs,
  ]);

  const [searchQuery, setSearchQuery] = useState("");

  // Search users query
  const { data: searchUsersData } = useSearchUsersQuery(searchQuery);

  const [selectedId, setSelectedId] = useState<string | null>(cid || null);

  // Conversation creation/editing state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newConvName, setNewConvName] = useState("");
  const [newConvDesc, setNewConvDesc] = useState("");
  const [editingConv, setEditingConv] = useState<Conversation | null>(null);
  const [editConvName, setEditConvName] = useState("");
  const [editConvDesc, setEditConvDesc] = useState("");

  // Conversation mutations
  const createConv = useCreateConversationMutation();
  const updateConv = useUpdateConversationMutation();
  const deleteConv = useDeleteConversationMutation();

  // Select first conversation by default
  useEffect(() => {
    if (
      !selectedId &&
      conversationsData?.conversations &&
      conversationsData.conversations.length > 0
    ) {
      setSelectedId(conversationsData.conversations[0].id);
    }
  }, [conversationsData, selectedId]);

  // Conversation details
  const {
    data: selectedConv,
    isLoading: convLoading,
    isFetching: convFetching,
  } = useGetConversationQuery(selectedId || "", messagePage);

  // Reset message pagination when conversation changes
  useEffect(() => {
    if (selectedId) {
      setMessagePage(1);
      setAllMessages([]);
    }
  }, [selectedId]);

  // Message mutations
  const createMsg = useCreateMessageMutation();
  const updateMsg = useUpdateMessageMutation();
  const deleteMsg = useDeleteMessageMutation();

  // Message input state
  const [msgInput, setMsgInput] = useState("");
  const [editingMsgId, setEditingMsgId] = useState<string | null>(null);
  const [editingMsgContent, setEditingMsgContent] = useState("");

  // Conversation and message deletion state
  const [convToLeave, setConvToLeave] = useState<Conversation | null>(null);
  const [msgToDelete, setMsgToDelete] = useState<{
    id: string;
    conversationId: string;
  } | null>(null);

  // New state for conversation participants in creation modal
  const [newConvParticipants, setNewConvParticipants] = useState<User[]>([]);

  // Add these new state variables near the other state declarations
  const [showParticipantConfirm, setShowParticipantConfirm] = useState(false);
  const [participantChanges, setParticipantChanges] = useState<{
    added: User[];
    removed: User[];
  }>({ added: [], removed: [] });

  const [lastScrollPosition, setLastScrollPosition] = useState<number>(0);

  useEffect(() => {
    if (conversationListRef.current && lastScrollPosition > 0) {
      conversationListRef.current.scrollTop = lastScrollPosition;
    }
  }, [selectedId, lastScrollPosition]);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  // useEffect(() => {
  //   scrollToBottom();
  // }, [selectedConv?.messages]);

  const handleEditModalClose = () => {
    setEditingConv(null);
    setEditConvName("");
    setEditConvDesc("");
    setSearchQuery("");
  };

  const formatMessageTime = (date: Date) => {
    const messageDate = dayjs(date);
    const now = dayjs();

    if (messageDate.isSame(now, "day")) {
      return `Today at ${messageDate.format("h:mm A")}`;
    }

    if (messageDate.isSame(now.subtract(1, "day"), "day")) {
      return `Yesterday at ${messageDate.format("h:mm A")}`;
    }

    return messageDate.format("MMM D [at] h:mm A");
  };

  // Add this near your other effect hooks
  useEffect(() => {
    // Create a stable function reference using useCallback
    const updateLastSeen = async () => {
      if (selectedId && user?.id) {
        try {
          await updateConv.mutateAsync({
            id: selectedId,
            body: {
              participantLastSeen: true,
            },
          });
          // Only invalidate the specific conversation query instead of all conversations
          await queryClient.invalidateQueries({
            queryKey: ["conversation", selectedId],
          });
          await queryClient.invalidateQueries({
            queryKey: ["users", user?.name, "conversations"],
          });
          // Remove the refreshConversations call as it's causing unnecessary reloads
        } catch (error) {
          console.error("Error updating last seen:", error);
        }
      }
    };
    updateLastSeen();
    // We only want this to run when the conversation changes or user changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedId, user?.id]); // Keep minimal dependencies

  // Handle message data updates with infinite scroll
  useEffect(() => {
    if (selectedConv?.messages) {
      if (messagePage === 1) {
        // First page - replace all messages and scroll to bottom
        setAllMessages([...selectedConv.messages]);
        setTimeout(() => scrollToBottom(), 100);
      } else {
        // Additional pages - prepend older messages
        setAllMessages((prev) => {
          const existingIds = new Set(prev.map((msg) => msg.id));
          const newMessages = selectedConv.messages.filter(
            (msg) => !existingIds.has(msg.id)
          );

          if (newMessages.length === 0) return prev;

          // Sort new messages and prepend to existing
          const sortedNewMessages = newMessages.sort(
            (a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );

          return [...sortedNewMessages, ...prev];
        });
      }
    }
  }, [selectedConv?.messages, messagePage]);

  // Message infinite scroll hook for upward scrolling
  const useMessageInfiniteScroll = (
    containerRef: React.RefObject<HTMLElement>,
    hasMore: boolean,
    isLoading: boolean,
    loadMore: () => void
  ) => {
    const [isFetching, setIsFetching] = useState(false);
    const scrollAnchorRef = useRef<{
      messageId: string;
      offsetFromTop: number;
      scrollHeight: number;
    }>({ messageId: "", offsetFromTop: 0, scrollHeight: 0 });

    useEffect(() => {
      const handleScroll = () => {
        const container = containerRef.current;
        if (!container || !hasMore || isLoading || isFetching) return;

        const { scrollTop } = container;
        const threshold = 5; // Load when 5px from top

        if (scrollTop <= threshold) {
          // Store scroll anchor before loading - use the first visible message
          const firstMessage = container.querySelector("[data-message-id]");
          if (firstMessage) {
            const messageId = firstMessage.getAttribute("data-message-id");
            if (messageId) {
              scrollAnchorRef.current = {
                messageId,
                offsetFromTop: scrollTop,
                scrollHeight: container.scrollHeight,
              };
            }
          }

          setIsFetching(true);
          loadMore();
        }
      };

      const container = containerRef.current;
      if (container) {
        container.addEventListener("scroll", handleScroll, { passive: true });
        return () => container.removeEventListener("scroll", handleScroll);
      }
    }, [hasMore, isLoading, isFetching, loadMore, containerRef]);

    useEffect(() => {
      if (!isLoading && isFetching) {
        // Restore scroll position after new messages are loaded
        const container = containerRef.current;
        const anchor = scrollAnchorRef.current;

        if (container && anchor.messageId) {
          // Find the anchor message element after new messages have been added
          const anchorElement = container.querySelector(
            `[data-message-id="${anchor.messageId}"]`
          );

          if (anchorElement) {
            // Calculate how much the content has grown
            const heightDifference =
              container.scrollHeight - anchor.scrollHeight;

            // Set scroll position to maintain the same visual position
            container.scrollTop = anchor.offsetFromTop + heightDifference;
          }

          setIsFetching(false);
        } else {
          setIsFetching(false);
        }
      }
    }, [isLoading, isFetching]);

    return isFetching;
  };

  // Use message infinite scroll
  const isMessageFetching = useMessageInfiniteScroll(
    messagesContainerRef,
    selectedConv?.hasMoreMessages || false, // Assuming your API returns this
    convFetching,
    () => setMessagePage((prev) => prev + 1)
  );

  const handleFilterClick = (newFilter: "linked" | "created") => {
    const newFilterValue = filter === newFilter ? "all" : newFilter;

    // Reset pagination and conversations
    setPage(1);
    setConversations([]); // This forces the component to show loading state
    setFilter(newFilterValue);
  };

  // Update your refreshConversations function:
  const refreshConversations = () => {
    setPage(1);
    setConversations([]); // Clear conversations first
    refetchConvs();
  };

  // Only scroll to bottom on new messages (not when loading older messages)
  useEffect(() => {
    if (messagePage === 1) {
      scrollToBottom();
    }
  }, [allMessages, messagePage]);

  return (
    <Box className="flex flex-row h-[80vh] gap-4 p-4">
      {/* Conversation List */}
      <Box className="w-1/3 min-w-[320px] max-w-[400px] bg-bgdark rounded-xl p-4 flex flex-col gap-4">
        <Box className="flex flex-row items-center gap-2">
          <ChatBubbleOutline className="text-slate-400" fontSize="medium" />
          <BaseText variant={Fonts.BodySemibold} className=" text-slate-300">
            Conversations
          </BaseText>
          <Button
            variant="contained"
            color="primary"
            sx={{
              ...buttonStyles.primaryButtonStyles,
              ml: "auto",
              minWidth: 0,
              width: 36,
              height: 36,
              borderRadius: "50%",
              background: "#4338ca",
              boxShadow: "none",
            }}
            onClick={() => setIsCreateModalOpen(true)}
          >
            <Add />
          </Button>
        </Box>
        <BaseText variant={Fonts.Caption200Semibold} className="text-slate-300">
          Filters
        </BaseText>
        <Box className="flex flex-row items-center gap-2 -mt-3">
          <Button
            variant="contained"
            sx={{
              ...buttonStyles.primaryButtonStyles,
              background: filter === "linked" ? "#4338ca" : "#333333",
              minWidth: 0,
              height: 25,
              fontSize: "12px",
              borderRadius: "16px",
              opacity: filter === "linked" ? 1 : 0.5,
            }}
            onClick={() => handleFilterClick("linked")}
          >
            <BaseText variant={Fonts.Caption200Regular} className="pt-[1px]">
              Project
            </BaseText>
          </Button>
          <Button
            variant="contained"
            sx={{
              ...buttonStyles.primaryButtonStyles,
              background: filter === "created" ? "#4338ca" : "#333333",
              minWidth: 0,
              height: 25,
              fontSize: "12px",
              borderRadius: "16px",
              opacity: filter === "created" ? 1 : 0.5,
            }}
            onClick={() => handleFilterClick("created")}
          >
            <BaseText variant={Fonts.Caption200Regular} className="pt-[1px]">
              Created
            </BaseText>
          </Button>
        </Box>
        <Box
          ref={conversationListRef}
          className="flex flex-col gap-2 overflow-y-auto max-h-[70vh]"
        >
          {loadingConvs && page === 1 ? (
            <CircularProgress size={24} className="mx-auto my-8" />
          ) : !conversations || conversations.length === 0 ? (
            <BaseText
              variant={Fonts.BodyRegular}
              className="text-slate-400 text-center"
            >
              No conversations yet.
            </BaseText>
          ) : (
            <>
              {conversations.map((conv) => {
                // Find current user's participant record
                const currentUserParticipant = conv.participants?.find(
                  (p) => p.user.id === user?.id
                );

                // Count unread messages
                const unreadCount =
                  conv.messages?.filter(
                    (msg) =>
                      new Date(msg.createdAt) >
                      new Date(currentUserParticipant?.lastSeenAt || 0)
                  ).length || 0;

                return (
                  <Box
                    key={conv.id}
                    className={`flex flex-row items-center gap-3 px-3 py-2 rounded-lg cursor-pointer hover:bg-[#232b3b] transition-all ${
                      selectedId === conv.id ? "bg-[#232b3b]" : ""
                    }`}
                    onClick={() => {
                      // Save scroll position before changing selection
                      if (conversationListRef.current) {
                        setLastScrollPosition(
                          conversationListRef.current.scrollTop
                        );
                      }
                      setSelectedId(conv.id);
                    }}
                  >
                    {unreadCount > 0 && (
                      <Box
                        className="px-2 py-0.5 rounded-full"
                        sx={{ background: "#4338ca" }}
                      >
                        <BaseText
                          variant={Fonts.Caption100Semibold}
                          className="text-white"
                        >
                          {unreadCount}
                        </BaseText>
                      </Box>
                    )}
                    <Avatar
                      sx={{
                        background: BGStyles.CHROME,
                        width: 36,
                        height: 36,
                      }}
                    >
                      <BaseText variant={Fonts.BodySemibold}>
                        {conv.name?.charAt(0).toUpperCase() || "C"}
                      </BaseText>
                    </Avatar>
                    <Box className="flex flex-col flex-1 overflow-hidden">
                      <Box className="flex flex-row items-center gap-2">
                        <BaseText
                          variant={Fonts.BodySemibold}
                          className="truncate text-slate-300"
                        >
                          {conv.name || "Untitled"}
                        </BaseText>
                      </Box>
                      <Box title={conv.description || ""}>
                        <BaseText
                          variant={Fonts.Caption200Regular}
                          className="text-slate-400 text-xs truncate"
                        >
                          {conv.description || ""}
                        </BaseText>
                      </Box>
                      {conv.project && (
                        <Box className="flex flex-row items-center gap-1 bg-accent w-fit px-2 rounded-lg mt-1 hover:bg-[#3730a3] transition-all duration-100">
                          <Link
                            href={`/projects/${conv.project?.id}`}
                            className="flex flex-row items-center gap-1"
                          >
                            <LinkRounded
                              sx={{ fontSize: 14 }}
                              className="text-white -mt-[2px]"
                            />
                            <BaseText
                              variant={Fonts.Caption100Regular}
                              className="text-white pb-[1px] pt-[2px] truncate"
                            >
                              {conv.project?.name || ""}
                            </BaseText>
                          </Link>
                        </Box>
                      )}
                    </Box>
                    {!conv.projectId && (
                      <>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingConv({
                              ...conv,
                              participants: conv.participants || [],
                            });
                            setEditConvName(conv.name || "");
                            setEditConvDesc(conv.description || "");
                            setSearchQuery("");
                          }}
                        >
                          <Edit fontSize="small" className="text-slate-400" />
                        </IconButton>
                        {/* Conversation delete icon */}
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setConvToLeave(conv);
                          }}
                        >
                          <Delete fontSize="small" className="text-slate-400" />
                        </IconButton>
                      </>
                    )}
                  </Box>
                );
              })}

              {/* Infinite scroll loading indicator */}
              {(isFetching || (loadingConvs && page > 1)) && (
                <Box className="flex justify-center py-4">
                  <CircularProgress size={20} />
                </Box>
              )}

              {/* End of list indicator */}
              {!conversationsData?.hasMore && conversations.length > 0 && (
                <Box className="text-center py-4">
                  <BaseText
                    variant={Fonts.Caption200Regular}
                    className="text-slate-500"
                  >
                    No more conversations
                  </BaseText>
                </Box>
              )}
            </>
          )}
        </Box>
      </Box>

      {/* Conversation View */}
      <Box className="flex-1 bg-bgdark rounded-xl p-4 flex flex-col gap-4">
        {convLoading || !selectedConv ? (
          <Box className="flex flex-1 items-center justify-center">
            {convLoading && (
              <>
                <CircularProgress size={24} />
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-400 mt-4"
                >
                  Loading conversation...
                </BaseText>
              </>
            )}
            {!selectedConv && !convLoading && (
              <BaseText variant={Fonts.BodyRegular} className="text-slate-400">
                Select a conversation to view messages.
              </BaseText>
            )}
          </Box>
        ) : (
          <>
            {/* Conversation Header */}
            <Box className="flex flex-row items-center gap-3 mb-2">
              <Avatar
                sx={{ background: BGStyles.CHROME, width: 40, height: 40 }}
              >
                <BaseText
                  variant={Fonts.HeadlineSemibold}
                  className="text-white"
                >
                  {selectedConv.name?.charAt(0).toUpperCase() || "C"}
                </BaseText>
              </Avatar>
              <Box className="flex flex-col mr-4">
                <BaseText
                  variant={Fonts.Title100}
                  className="text-slate-300 truncate"
                >
                  {selectedConv.name || "Untitled"}
                </BaseText>
                {selectedConv.description && (
                  <BaseText
                    variant={Fonts.Caption200Regular}
                    className="text-slate-400"
                  >
                    {selectedConv.description || ""}
                  </BaseText>
                )}
              </Box>
              {selectedConv.participants && (
                <Box className="flex flex-row items-center gap-2 flex-wrap">
                  {selectedConv.participants.slice(0, 5).map((participant) => (
                    <Link
                      key={participant.id}
                      href={`/users/${participant.user.name}`}
                    >
                      <Box
                        key={participant.id}
                        className="p-2 bg-bglight px-4 hover:bg-accent rounded-full cursor-pointer flex flex-row items-center gap-2"
                        sx={{
                          background:
                            participant.user.id === user?.id
                              ? BGStyles.CHROME
                              : undefined,
                        }}
                      >
                        <Avatar
                          src={participant.user.profileImg}
                          sx={{
                            background: "#4338ca",
                            width: 24,
                            height: 24,
                            fontSize: "14px",
                          }}
                        >
                          {participant.user.name.charAt(0).toUpperCase()}
                        </Avatar>
                        <BaseText
                          variant={Fonts.BodyRegular}
                          className="text-slate-300 pt-[0.5px]"
                        >
                          {participant.user.name}
                        </BaseText>
                      </Box>
                    </Link>
                  ))}
                  {selectedConv.participants.length > 5 && (
                    <Box className="group">
                      <Box className="p-2 bg-bglight px-4 rounded-full flex flex-row items-center ">
                        <BaseText
                          variant={Fonts.BodyRegular}
                          className="text-slate-400"
                        >
                          +{selectedConv.participants.length - 5} more
                        </BaseText>
                      </Box>
                      <Box className="ml-2 flex-col group-hover:flex hidden absolute bg-bglight rounded-lg p-2 shadow-lg z-10 max-h-[200px] overflow-y-auto">
                        {selectedConv.participants
                          .slice(5)
                          .map((participant) => (
                            <Link
                              key={participant.id}
                              href={`/users/${participant.user.name}`}
                            >
                              <BaseText
                                variant={Fonts.FootnoteRegular}
                                className="text-slate-300 hover:text-white cursor-pointer p-1"
                              >
                                {participant.user.name}
                              </BaseText>
                            </Link>
                          ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
            {/* Messages */}
            <Box
              className="flex-1 flex flex-col gap-4 overflow-y-auto max-h-[60vh] px-2 pt-5"
              ref={messagesContainerRef}
            >
              {allMessages.length === 0 && !convLoading ? (
                <BaseText
                  variant={Fonts.BodyRegular}
                  className="text-slate-400 text-center mt-8"
                >
                  No messages yet. Start the conversation!
                </BaseText>
              ) : (
                <>
                  {(() => {
                    const currentUserParticipant =
                      selectedConv.participants?.find(
                        (p) => p.user.id === user?.id
                      );
                    const lastSeenAt = currentUserParticipant?.lastSeenAt;
                    let hasShownUnreadMarker = false;

                    return allMessages.map((msg, index) => {
                      const isUnread =
                        lastSeenAt &&
                        new Date(msg.createdAt) > new Date(lastSeenAt);
                      const shouldShowUnreadMarker =
                        isUnread && !hasShownUnreadMarker;

                      if (shouldShowUnreadMarker) {
                        hasShownUnreadMarker = true;
                      }

                      return (
                        <div key={msg.id} data-message-id={msg.id}>
                          {shouldShowUnreadMarker && (
                            <Box
                              className="flex items-center gap-2 my-4 px-4"
                              key={`unread-marker-${msg.id}`}
                            >
                              <div className="h-[1px] flex-1 bg-indigo-600" />
                              <BaseText
                                variant={Fonts.Caption200Regular}
                                className="text-indigo-600"
                              >
                                New Messages
                              </BaseText>
                              <div className="h-[1px] flex-1 bg-indigo-600" />
                            </Box>
                          )}
                          <Box
                            key={msg.id}
                            className={`group flex flex-row gap-3 items-start ${
                              Number(msg.senderId) === user?.id
                                ? "justify-end"
                                : "justify-start"
                            }`}
                          >
                            {Number(msg.senderId) !== user?.id && (
                              <Avatar
                                src={msg.sender.profileImg}
                                sx={{
                                  bgcolor: "#64748b",
                                  width: 32,
                                  height: 32,
                                }}
                              >
                                <BaseText variant={Fonts.SubheadlineRegular}>
                                  {msg.sender.name?.charAt(0).toUpperCase() ||
                                    "U"}
                                </BaseText>
                              </Avatar>
                            )}
                            <Box className="flex flex-col relative max-w-[60%] -top-5">
                              <BaseText
                                variant={Fonts.FootnoteRegular}
                                className={`text-slate-500 flex-1 ${
                                  Number(msg.senderId) === user?.id
                                    ? "text-right"
                                    : "text-left"
                                }`}
                              >
                                {msg.sender.name || ""}
                              </BaseText>
                              <Box
                                className={`rounded-lg px-4 py-2 ${
                                  Number(msg.senderId) === user?.id
                                    ? "bg-[#4338ca] text-white"
                                    : "bg-[#232b3b] text-slate-200"
                                }`}
                              >
                                {/* Top row: edit/delete icons for own messages */}
                                {Number(msg.senderId) === user?.id && (
                                  <Box className="flex-row-reverse bg-accent absolute top-5 right-1 z-10 hidden group-hover:flex">
                                    {editingMsgId !== msg.id && (
                                      <>
                                        <IconButton
                                          size="small"
                                          onClick={() => {
                                            setEditingMsgId(msg.id);
                                            setEditingMsgContent(msg.content);
                                          }}
                                        >
                                          <Edit
                                            fontSize="small"
                                            className="text-slate-300"
                                          />
                                        </IconButton>
                                        {/* Message delete icon */}
                                        <IconButton
                                          size="small"
                                          onClick={() => {
                                            setMsgToDelete({
                                              id: msg.id,
                                              conversationId: selectedConv.id,
                                            });
                                          }}
                                        >
                                          <Delete
                                            fontSize="small"
                                            className="text-slate-300"
                                          />
                                        </IconButton>
                                      </>
                                    )}
                                  </Box>
                                )}

                                {/* Message content and edit form */}
                                <Box>
                                  {editingMsgId === msg.id ? (
                                    <Box className="flex flex-col gap-1">
                                      <input
                                        type="text"
                                        value={editingMsgContent}
                                        onChange={(e) =>
                                          setEditingMsgContent(e.target.value)
                                        }
                                        className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
                                        autoFocus
                                        onKeyDown={(e) => {
                                          if (e.key === "Enter") {
                                            updateMsg.mutate(
                                              {
                                                id: msg.id,
                                                body: {
                                                  content: editingMsgContent,
                                                },
                                              },
                                              {
                                                onSuccess: () => {
                                                  setEditingMsgId(null);
                                                  setEditingMsgContent("");
                                                },
                                              }
                                            );
                                          }
                                        }}
                                        placeholder="Edit message..."
                                      />
                                      <Box className="flex flex-row gap-1 mt-1 justify-end items-center">
                                        <Button
                                          className="flex-1"
                                          sx={{
                                            ...buttonStyles.primaryButtonStyles,
                                            background: "#008888",
                                            border: "2px solid #008888",
                                            "&:hover": {
                                              background: "#006666",
                                              border: "2px solid #006666",
                                              scale: 1.05,
                                            },
                                          }}
                                          onClick={() => {
                                            updateMsg.mutate(
                                              {
                                                id: msg.id,
                                                body: {
                                                  content: editingMsgContent,
                                                },
                                              },
                                              {
                                                onSuccess: () => {
                                                  setEditingMsgId(null);
                                                  setEditingMsgContent("");
                                                },
                                              }
                                            );
                                          }}
                                        >
                                          <BaseText
                                            variant={Fonts.Caption200Semibold}
                                          >
                                            Save
                                          </BaseText>
                                        </Button>
                                        <Button
                                          className="flex-1"
                                          sx={{
                                            ...buttonStyles.outlinedDangerButtonStyles,
                                            background: "#a81a1a",
                                            border: "2px solid #a81a1a",
                                            color: "#cbd5e1",
                                          }}
                                          onClick={() => setEditingMsgId(null)}
                                        >
                                          <BaseText
                                            variant={Fonts.Caption200Semibold}
                                          >
                                            Cancel
                                          </BaseText>
                                        </Button>
                                      </Box>
                                    </Box>
                                  ) : (
                                    <BaseText variant={Fonts.BodyRegular}>
                                      {msg.content}
                                    </BaseText>
                                  )}
                                  <BaseText
                                    variant={Fonts.Caption200Regular}
                                    className="text-slate-400 pt-2"
                                  >
                                    {formatMessageTime(msg.createdAt)}
                                    {msg.updatedAt !== msg.createdAt &&
                                      " (edited)"}
                                  </BaseText>
                                </Box>
                              </Box>
                            </Box>
                            {Number(msg.senderId) === user?.id && (
                              <Avatar
                                src={user?.profileImg}
                                sx={{
                                  bgcolor: "#4338ca",
                                  width: 32,
                                  height: 32,
                                }}
                              >
                                <BaseText variant={Fonts.SubheadlineRegular}>
                                  {user?.name?.charAt(0).toUpperCase() || "U"}
                                </BaseText>
                              </Avatar>
                            )}
                          </Box>
                        </div>
                      );
                    });
                  })()}
                </>
              )}
            </Box>
            {/* Message Input */}
            <Box className="flex flex-row gap-2 mt-10">
              <input
                type="text"
                className="flex-1 bg-[#232b3b] text-slate-200 p-2 px-4 rounded-lg outline-none"
                placeholder="Type a message..."
                value={msgInput}
                onChange={(e) => setMsgInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && msgInput.trim()) {
                    createMsg.mutate(
                      {
                        conversationId: selectedConv.id,
                        content: msgInput,
                      },
                      {
                        onSuccess: (newMessage) => {
                          setMsgInput("");
                          setAllMessages((prev) => [...prev, newMessage]);
                          setTimeout(() => scrollToBottom(), 100);
                          if (selectedConv) {
                            setConversations((prev) => {
                              const updatedConversations = prev.filter(
                                (c) => c.id !== selectedConv.id
                              );
                              return [selectedConv, ...updatedConversations];
                            });
                          }
                        },
                      }
                    );
                  }
                }}
                autoFocus
              />
              <Button
                sx={{
                  ...buttonStyles.primaryButtonStyles,
                  minWidth: 0,
                  width: 48,
                  height: 48,
                  borderRadius: "50%",
                  background: "#4338ca",
                  boxShadow: "none",
                }}
                disabled={!msgInput.trim()}
                onClick={() => {
                  if (!msgInput.trim()) return;
                  createMsg.mutate(
                    {
                      conversationId: selectedConv.id,
                      content: msgInput,
                    },
                    {
                      onSuccess: (newMessage) => {
                        setMsgInput("");
                        setAllMessages((prev) => [...prev, newMessage]);
                        setTimeout(() => scrollToBottom(), 100);
                        if (selectedConv) {
                          setConversations((prev) => {
                            const updatedConversations = prev.filter(
                              (c) => c.id !== selectedConv.id
                            );
                            return [selectedConv, ...updatedConversations];
                          });
                        }
                      },
                    }
                  );
                }}
              >
                <Send />
              </Button>
            </Box>
          </>
        )}
      </Box>

      {/* Create Conversation Modal */}
      <Modal
        title="New Conversation"
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      >
        <Box className="flex flex-col gap-4 w-[500px] max-w-full">
          <Box>
            <BaseText variant={Fonts.BodySemibold} className="mb-2">
              Conversation Name
            </BaseText>
            <input
              type="text"
              value={newConvName}
              onChange={(e) => setNewConvName(e.target.value)}
              className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
              placeholder="Enter conversation name"
              autoFocus
              maxLength={25}
            />
          </Box>
          <Box>
            <BaseText variant={Fonts.BodySemibold} className="mb-2">
              Description
            </BaseText>
            <input
              type="text"
              value={newConvDesc}
              onChange={(e) => setNewConvDesc(e.target.value)}
              className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
              placeholder="Enter description"
            />
          </Box>
          <Box>
            <BaseText variant={Fonts.BodySemibold} className="mb-2">
              Participants
            </BaseText>
            <Box className="flex flex-wrap gap-2 mb-2">
              {/* Current user chip */}
              <Box
                className="px-2 py-1 rounded-full flex items-center gap-2"
                sx={{
                  background: BGStyles.CHROME,
                }}
              >
                <Avatar
                  src={user?.profileImg}
                  sx={{
                    background: "#4338ca",
                    width: 24,
                    height: 24,
                    fontSize: "14px",
                  }}
                >
                  {user?.name.charAt(0).toUpperCase()}
                </Avatar>
                <BaseText
                  variant={Fonts.FootnoteRegular}
                  className="text-white pt-[0.5px]"
                >
                  {user?.name} (You)
                </BaseText>
              </Box>
              {/* Selected participants chips */}
              {newConvParticipants.map((p) => (
                <Box
                  key={p.id}
                  className="bg-bglight px-2 py-1 rounded-full flex items-center gap-2"
                >
                  <Avatar
                    src={p.profileImg}
                    sx={{
                      background: "#4338ca",
                      width: 24,
                      height: 24,
                      fontSize: "14px",
                    }}
                  >
                    {p.name.charAt(0).toUpperCase()}
                  </Avatar>
                  <BaseText
                    variant={Fonts.FootnoteRegular}
                    className="text-slate-300 pt-[0.5px] truncate"
                  >
                    {p.name}
                  </BaseText>
                  <IconButton
                    size="small"
                    onClick={() =>
                      setNewConvParticipants((prev) =>
                        prev.filter((part) => part.id !== p.id)
                      )
                    }
                  >
                    <Close fontSize="small" className="text-slate-400 -ml-2" />
                  </IconButton>
                </Box>
              ))}
            </Box>
            {/* Search input and dropdown */}
            <Box className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
                placeholder="Search users..."
              />
              {searchQuery && searchUsersData && (
                <Box className="absolute top-full left-0 right-0 bg-bglight mt-1 rounded-lg max-h-48 overflow-y-auto z-50">
                  {searchUsersData
                    .filter(
                      (u) =>
                        u.id !== user?.id &&
                        !newConvParticipants.some((p) => Number(p.id) === u.id)
                    )
                    .map((u) => (
                      <Box
                        key={u.id}
                        className="p-2 hover:bg-accent cursor-pointer flex flex-row items-center gap-2"
                        onClick={() => {
                          setNewConvParticipants((prev) => [...prev, u]);
                          setSearchQuery("");
                        }}
                      >
                        <Avatar
                          src={u.profileImg}
                          sx={{
                            background: "#4338ca",
                            width: 24,
                            height: 24,
                            fontSize: "14px",
                          }}
                        >
                          {u.name.charAt(0).toUpperCase()}
                        </Avatar>
                        <BaseText
                          variant={Fonts.BodyRegular}
                          className="text-slate-300"
                        >
                          {u.name}
                        </BaseText>
                      </Box>
                    ))}
                </Box>
              )}
            </Box>
          </Box>

          <Box className="flex flex-row gap-2 justify-end mt-2">
            <Button
              sx={buttonStyles.outlinedDangerButtonStyles}
              onClick={() => setIsCreateModalOpen(false)}
              className="flex-1"
            >
              <BaseText variant={Fonts.Caption200Semibold}>Cancel</BaseText>
            </Button>
            <Button
              sx={buttonStyles.primaryButtonStyles}
              onClick={() => {
                createConv.mutate(
                  {
                    name: newConvName,
                    description: newConvDesc,
                    participantIds: [
                      user?.id ?? 0,
                      ...newConvParticipants.map((p) => Number(p.id)),
                    ],
                  },
                  {
                    onSuccess: () => {
                      setIsCreateModalOpen(false);
                      setNewConvName("");
                      setNewConvDesc("");
                      setNewConvParticipants([]);
                      setSearchQuery("");
                      refreshConversations();
                    },
                  }
                );
              }}
              disabled={!newConvName.trim()}
              className="flex-1"
            >
              <BaseText variant={Fonts.Caption200Semibold}>Create</BaseText>
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Edit Conversation Modal */}
      <Modal
        title="Edit Conversation"
        isOpen={!!editingConv}
        onClose={handleEditModalClose}
      >
        <Box className="flex flex-col gap-4 w-[500px] max-w-full">
          <Box>
            <BaseText variant={Fonts.BodySemibold} className="mb-2">
              Conversation Name
            </BaseText>
            <input
              type="text"
              value={editConvName}
              onChange={(e) => setEditConvName(e.target.value)}
              className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
              placeholder="Enter conversation name"
              autoFocus
              maxLength={25}
            />
          </Box>
          <Box>
            <BaseText variant={Fonts.BodySemibold} className="mb-2">
              Description
            </BaseText>
            <input
              type="text"
              value={editConvDesc}
              onChange={(e) => setEditConvDesc(e.target.value)}
              className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
              placeholder="Enter description"
            />
          </Box>
          <Box>
            <BaseText variant={Fonts.BodySemibold} className="mb-2">
              Participants
            </BaseText>
            <Box className="flex flex-wrap gap-2 mb-2">
              {/* Current participants chips */}
              {editingConv?.participants?.map((p) => (
                <Box
                  key={p.id}
                  className="bg-bglight px-2 py-1 rounded-full flex items-center gap-1"
                  sx={{
                    background:
                      p.user.id === user?.id ? BGStyles.CHROME : undefined,
                  }}
                >
                  <Avatar
                    src={p.user.profileImg}
                    sx={{
                      background: "#4338ca",
                      width: 24,
                      height: 24,
                      fontSize: "14px",
                    }}
                  >
                    {p.user.name.charAt(0).toUpperCase()}
                  </Avatar>
                  <BaseText
                    variant={Fonts.FootnoteRegular}
                    className="text-slate-300"
                  >
                    {p.user.name}{" "}
                    {p.user.id === user?.id
                      ? "(You)"
                      : p.user.id === Number(editingConv.authorId)
                      ? "(Owner)"
                      : ""}
                  </BaseText>
                  {p.user.id !== user?.id &&
                    p.user.id !== Number(editingConv.authorId) && (
                      <IconButton
                        size="small"
                        onClick={() => {
                          if (!editingConv) return;
                          setEditingConv({
                            ...editingConv,
                            participants: editingConv.participants?.filter(
                              (part) => part.id !== p.id
                            ),
                          });
                        }}
                      >
                        <Close fontSize="small" className="text-slate-400" />
                      </IconButton>
                    )}
                </Box>
              ))}
            </Box>
            {/* Search input and dropdown */}
            <Box className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-bglight text-slate-300 p-2 rounded-lg"
                placeholder="Search users..."
              />
              {searchQuery && searchUsersData && (
                <Box className="absolute top-full left-0 right-0 bg-bglight mt-1 rounded-lg max-h-48 overflow-y-auto z-50">
                  {searchUsersData
                    .filter(
                      (u) =>
                        u.id !== user?.id &&
                        !editingConv?.participants?.some(
                          (p) => p.user.id === u.id
                        )
                    )
                    .map((u) => (
                      <Box
                        key={u.id}
                        className="p-2 hover:bg-accent cursor-pointer flex flex-row items-center gap-2"
                        onClick={() => {
                          if (!editingConv) return;
                          setEditingConv({
                            ...editingConv,
                            participants: [
                              ...(editingConv.participants || []),
                              {
                                user: { id: u.id, name: u.name } as User,
                              } as ConversationParticipant,
                            ],
                          });
                          setSearchQuery("");
                        }}
                      >
                        <Avatar
                          src={u.profileImg}
                          sx={{
                            background: "#4338ca",
                            width: 24,
                            height: 24,
                            fontSize: "14px",
                          }}
                        >
                          {u.name.charAt(0).toUpperCase()}
                        </Avatar>
                        <BaseText
                          variant={Fonts.BodyRegular}
                          className="text-slate-300"
                        >
                          {u.name}
                        </BaseText>
                      </Box>
                    ))}
                </Box>
              )}
            </Box>
          </Box>

          <Box className="flex flex-row gap-2 justify-end mt-2">
            <Button
              sx={buttonStyles.outlinedDangerButtonStyles}
              onClick={() => setEditingConv(null)}
              className="flex-1"
            >
              <BaseText variant={Fonts.Caption200Semibold}>Cancel</BaseText>
            </Button>
            <Button
              sx={buttonStyles.primaryButtonStyles}
              onClick={() => {
                if (!editingConv) return;

                // Calculate participant changes
                const originalParticipants = conversationsData?.conversations
                  .find((c) => c.id === editingConv.id)
                  ?.participants?.map((p) => p.user);
                if (!originalParticipants) return;
                const newParticipants = editingConv.participants?.map(
                  (p) => p.user
                );

                const added =
                  newParticipants?.filter(
                    (p) => !originalParticipants?.find((op) => op.id === p.id)
                  ) || [];
                const removed =
                  originalParticipants?.filter(
                    (p) => !newParticipants?.find((np) => np.id === p.id)
                  ) || [];

                if (
                  (added && added?.length > 0) ||
                  (removed && removed?.length > 0)
                ) {
                  setParticipantChanges({ added, removed });
                  setShowParticipantConfirm(true);
                } else {
                  // No participant changes, proceed with update
                  updateConv.mutate(
                    {
                      id: editingConv.id,
                      body: {
                        name: editConvName,
                        description: editConvDesc,
                        participantIds: editingConv.participants?.map(
                          (p) => p.user.id
                        ),
                      },
                    },
                    {
                      onSuccess: () => {
                        refreshConversations();
                        setEditingConv(null);
                      },
                    }
                  );
                }
              }}
              disabled={!editConvName.trim()}
              className="flex-1"
            >
              <BaseText variant={Fonts.Caption200Semibold}>Save</BaseText>
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Leave Conversation Confirmation Modal */}
      <Modal
        title="Leave Conversation?"
        isOpen={!!convToLeave}
        onClose={() => setConvToLeave(null)}
      >
        <BaseText
          variant={Fonts.BodySemibold}
          className="mb-4 mt-3 text-center"
        >
          Are you sure you want to leave this conversation?
        </BaseText>
        <Box className="flex flex-row gap-4 justify-center mt-4">
          <Button
            variant="outlined"
            color="secondary"
            className="text-md normal-case"
            sx={buttonStyles.outlinedDangerButtonStyles}
            onClick={async () => {
              if (!convToLeave) return;
              const newParticipants = convToLeave.participants
                ?.filter((p) => p.user.id !== user?.id)
                .map((p) => p.user.id);
              if (!newParticipants || newParticipants.length === 0) {
                deleteConv.mutate(convToLeave.id, {
                  onSuccess: () => {
                    if (selectedId === convToLeave.id) setSelectedId(null);
                    setConvToLeave(null);
                    refreshConversations();
                  },
                });
              } else {
                updateConv.mutate(
                  {
                    id: convToLeave.id,
                    body: {
                      participantIds: newParticipants || [],
                    },
                  },
                  {
                    onSuccess: () => {
                      if (selectedId === convToLeave.id) setSelectedId(null);
                      setConvToLeave(null);
                      refreshConversations();
                    },
                    onError: (error) => {
                      console.error("Error leaving conversation:", error);
                      alert("Failed to leave conversation. Please try again.");
                    },
                  }
                );
              }
            }}
          >
            Confirm
          </Button>
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
            onClick={() => setConvToLeave(null)}
          >
            Cancel
          </Button>
        </Box>
      </Modal>

      {/* Delete Message Confirmation Modal */}
      <Modal
        title="Delete Message?"
        isOpen={!!msgToDelete}
        onClose={() => setMsgToDelete(null)}
      >
        <BaseText
          variant={Fonts.BodySemibold}
          className="mb-4 mt-3 text-center"
        >
          Are you sure you want to delete this message?
        </BaseText>
        <Box className="flex flex-row gap-4 justify-center mt-4">
          <Button
            variant="outlined"
            color="secondary"
            className="text-md normal-case"
            sx={buttonStyles.outlinedDangerButtonStyles}
            onClick={() => {
              if (!msgToDelete) return;
              deleteMsg.mutate(
                {
                  id: msgToDelete.id,
                  conversationId: msgToDelete.conversationId,
                },
                {
                  onSuccess: () => {
                    setMsgToDelete(null);
                  },
                  onError: (error) => {
                    console.error("Error deleting message:", error);
                    alert("Failed to delete message. Please try again.");
                  },
                }
              );
            }}
          >
            Confirm
          </Button>
          <Button
            variant="contained"
            color="primary"
            className="text-md normal-case"
            sx={buttonStyles.primaryButtonStyles}
            onClick={() => setMsgToDelete(null)}
          >
            Cancel
          </Button>
        </Box>
      </Modal>

      {/* Participant Changes Confirmation Modal */}
      <Modal
        title="Confirm Participant Changes"
        isOpen={showParticipantConfirm}
        onClose={() => setShowParticipantConfirm(false)}
      >
        <Box className="flex flex-col gap-4 w-[500px] max-w-full p-4">
          <BaseText variant={Fonts.BodySemibold} className="text-center">
            Are you sure you want to add/remove these participants?
          </BaseText>

          {participantChanges.removed.length > 0 && (
            <Box className="flex flex-col gap-2">
              <BaseText
                variant={Fonts.SubheadlineRegular}
                className="text-slate-600"
              >
                To be removed:
              </BaseText>
              <Box className="flex flex-wrap gap-2">
                {participantChanges.removed.map((p) => (
                  <Box
                    key={p.id}
                    className="px-3 py-1 rounded-full flex items-center gap-2"
                    sx={{ background: "#661a1a" }}
                  >
                    <Avatar
                      src={p.profileImg}
                      sx={{
                        background: "#4338ca",
                        width: 24,
                        height: 24,
                        fontSize: "14px",
                      }}
                    >
                      {p.name.charAt(0).toUpperCase()}
                    </Avatar>
                    <BaseText
                      variant={Fonts.FootnoteRegular}
                      className="text-slate-300"
                    >
                      {p.name}
                    </BaseText>
                  </Box>
                ))}
              </Box>
            </Box>
          )}

          {participantChanges.added.length > 0 && (
            <Box className="flex flex-col gap-2">
              <BaseText
                variant={Fonts.SubheadlineRegular}
                className="text-slate-600"
              >
                To be added:
              </BaseText>
              <Box className="flex flex-wrap gap-2">
                {participantChanges.added.map((p) => (
                  <Box
                    key={p.id}
                    className="px-3 py-1 rounded-full flex items-center gap-2"
                    sx={{ background: "#166634" }}
                  >
                    <Avatar
                      src={p.profileImg}
                      sx={{
                        background: "#4338ca",
                        width: 24,
                        height: 24,
                        fontSize: "14px",
                      }}
                    >
                      {p.name.charAt(0).toUpperCase()}
                    </Avatar>
                    <BaseText
                      variant={Fonts.FootnoteRegular}
                      className="text-slate-300"
                    >
                      {p.name}
                    </BaseText>
                  </Box>
                ))}
              </Box>
            </Box>
          )}

          <Box className="flex flex-row gap-4 justify-center mt-4">
            <Button
              variant="outlined"
              className="text-md normal-case"
              sx={buttonStyles.outlinedDangerButtonStyles}
              onClick={() => setShowParticipantConfirm(false)}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              className="text-md normal-case"
              sx={buttonStyles.primaryButtonStyles}
              onClick={() => {
                if (!editingConv) return;
                updateConv.mutate(
                  {
                    id: editingConv.id,
                    body: {
                      name: editConvName,
                      description: editConvDesc,
                      participantIds: editingConv.participants?.map(
                        (p) => p.user.id
                      ),
                    },
                  },
                  {
                    onSuccess: () => {
                      refreshConversations();
                      setEditingConv(null);
                      setShowParticipantConfirm(false);
                    },
                  }
                );
              }}
            >
              Confirm Changes
            </Button>
          </Box>
        </Box>
      </Modal>
    </Box>
  );
}
